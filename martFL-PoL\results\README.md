# Results Directory

This directory stores experimental results from POL-martFL integration tests.

## File Types

- **JSON files**: Numerical results and metrics
- **PNG files**: Visualization plots and charts
- **Log files**: Detailed execution logs

## Latest Results

Run the main experiment to generate fresh results:

```bash
python scripts/test_large_scale_multi_round.py
```

Results will be automatically saved with timestamps.
