"""
POL-Enhanced Aggregator for Federated Learning
集成POL验证的联邦学习聚合器
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import copy
import time
from collections import defaultdict

from pol_verifier import POLVerifier


class POLAggregator:
    """
    POL-Enhanced Aggregator
    集成POL验证机制的联邦学习聚合器
    """
    
    def __init__(self,
                 device: torch.device,
                 pol_verification_enabled: bool = True,
                 verification_budget: int = 5,
                 delta_threshold: float = 1e-3,
                 pol_weight_factor: float = 0.3,
                 min_pol_success_rate: float = 0.6):
        """
        初始化POL聚合器
        
        Args:
            device: 计算设备
            pol_verification_enabled: 是否启用POL验证
            verification_budget: POL验证预算
            delta_threshold: POL验证阈值
            pol_weight_factor: POL验证结果在聚合权重中的影响因子
            min_pol_success_rate: 最小POL验证成功率阈值
        """
        self.device = device
        self.pol_verification_enabled = pol_verification_enabled
        self.pol_weight_factor = pol_weight_factor
        self.min_pol_success_rate = min_pol_success_rate
        
        # 初始化POL验证器
        if self.pol_verification_enabled:
            self.pol_verifier = POLVerifier(
                device=device,
                verification_budget=verification_budget,
                delta_threshold=delta_threshold
            )
        
        # 聚合统计
        self.aggregation_stats = {
            'total_rounds': 0,
            'pol_verified_clients': 0,
            'pol_rejected_clients': 0,
            'aggregation_times': [],
            'verification_times': []
        }
        
        # 客户端信誉记录
        self.client_reputation = defaultdict(lambda: {'pol_success_count': 0, 'total_submissions': 0})
    
    def calibrate_pol_threshold(self,
                              honest_client_proofs: List[Dict[str, Any]],
                              model_architecture: nn.Module,
                              dataset: torch.utils.data.Dataset,
                              criterion: nn.Module,
                              optimizer_class: type,
                              optimizer_kwargs: Dict[str, Any]):
        """
        使用诚实客户端校准POL验证阈值

        Args:
            honest_client_proofs: 诚实客户端的POL证明列表
            其他参数同aggregate_with_pol
        """
        if self.pol_verification_enabled and honest_client_proofs:
            self.pol_verifier.calibrate_threshold_with_honest_baseline(
                honest_client_proofs,
                model_architecture,
                dataset,
                criterion,
                optimizer_class,
                optimizer_kwargs
            )

    def aggregate_with_pol(self,
                          client_models: List[nn.Module],
                          client_pol_proofs: List[Optional[Dict[str, Any]]],
                          client_weights: List[float],
                          global_model: nn.Module,
                          dataset: torch.utils.data.Dataset,
                          criterion: nn.Module,
                          optimizer_class: type,
                          optimizer_kwargs: Dict[str, Any],
                          client_ids: List[int],
                          calibrate_threshold: bool = False) -> Dict[str, Any]:
        """
        使用POL验证进行模型聚合
        
        Args:
            client_models: 客户端模型列表
            client_pol_proofs: 客户端POL证明列表
            client_weights: 客户端权重列表
            global_model: 全局模型
            dataset: 数据集
            criterion: 损失函数
            optimizer_class: 优化器类
            optimizer_kwargs: 优化器参数
            client_ids: 客户端ID列表
            
        Returns:
            聚合结果字典
        """
        start_time = time.time()
        
        # 验证输入
        n_clients = len(client_models)
        assert len(client_pol_proofs) == n_clients
        assert len(client_weights) == n_clients
        assert len(client_ids) == n_clients
        
        # 自动校准阈值（如果启用且未校准过）
        if (self.pol_verification_enabled and calibrate_threshold and
            hasattr(self.pol_verifier, 'threshold_calibrated') and
            not self.pol_verifier.threshold_calibrated):

            # 收集有POL证明的客户端（假设为诚实客户端用于校准）
            honest_proofs = [proof for proof in client_pol_proofs if proof is not None]
            if honest_proofs:
                print("Auto-calibrating POL threshold with available proofs...")
                self.calibrate_pol_threshold(
                    honest_proofs[:min(2, len(honest_proofs))],  # 使用前2个进行校准
                    client_models[0],  # 使用第一个模型的架构
                    dataset, criterion, optimizer_class, optimizer_kwargs
                )

        # POL验证阶段
        pol_verification_results = []
        verified_clients = []

        if self.pol_verification_enabled:
            verification_start = time.time()
            
            for i, (model, pol_proof, client_id) in enumerate(zip(client_models, client_pol_proofs, client_ids)):
                if pol_proof is not None:
                    # 执行POL验证
                    verification_result = self.pol_verifier.verify_pol_proof(
                        pol_proof=pol_proof,
                        model_architecture=model,
                        dataset=dataset,
                        criterion=criterion,
                        optimizer_class=optimizer_class,
                        optimizer_kwargs=optimizer_kwargs
                    )
                    
                    pol_verification_results.append(verification_result)
                    
                    # 更新客户端信誉
                    self.client_reputation[client_id]['total_submissions'] += 1
                    if verification_result['success']:
                        self.client_reputation[client_id]['pol_success_count'] += 1
                        verified_clients.append(i)
                        self.aggregation_stats['pol_verified_clients'] += 1
                    else:
                        self.aggregation_stats['pol_rejected_clients'] += 1
                        print(f"Client {client_id} POL verification failed: {verification_result['message']}")
                else:
                    # 没有POL证明的客户端
                    pol_verification_results.append({
                        'success': False,
                        'message': 'No POL proof provided'
                    })
                    print(f"Client {client_id} did not provide POL proof")
            
            verification_time = time.time() - verification_start
            self.aggregation_stats['verification_times'].append(verification_time)
            
        else:
            # 如果未启用POL验证，所有客户端都通过
            verified_clients = list(range(n_clients))
            pol_verification_results = [{'success': True, 'message': 'POL verification disabled'}] * n_clients
        
        # 如果没有客户端通过验证，返回原始全局模型
        if not verified_clients:
            print("Warning: No clients passed POL verification. Returning original global model.")
            return {
                'aggregated_model': global_model,
                'verified_clients': [],
                'pol_verification_results': pol_verification_results,
                'aggregation_weights': [],
                'aggregation_time': time.time() - start_time
            }
        
        # 计算POL-aware聚合权重
        pol_aware_weights = self._compute_pol_aware_weights(
            client_weights, pol_verification_results, verified_clients, client_ids
        )
        
        # 执行模型聚合
        aggregated_model = self._aggregate_models(
            [client_models[i] for i in verified_clients],
            pol_aware_weights,
            global_model
        )
        
        aggregation_time = time.time() - start_time
        self.aggregation_stats['aggregation_times'].append(aggregation_time)
        self.aggregation_stats['total_rounds'] += 1
        
        return {
            'aggregated_model': aggregated_model,
            'verified_clients': verified_clients,
            'pol_verification_results': pol_verification_results,
            'aggregation_weights': pol_aware_weights,
            'aggregation_time': aggregation_time,
            'client_reputation': dict(self.client_reputation)
        }
    
    def _compute_pol_aware_weights(self,
                                  original_weights: List[float],
                                  pol_results: List[Dict[str, Any]],
                                  verified_clients: List[int],
                                  client_ids: List[int]) -> List[float]:
        """
        计算POL-aware的聚合权重
        
        Args:
            original_weights: 原始权重
            pol_results: POL验证结果
            verified_clients: 通过验证的客户端索引
            client_ids: 客户端ID列表
            
        Returns:
            调整后的聚合权重
        """
        if not verified_clients:
            return []
        
        # 提取通过验证的客户端的原始权重
        verified_weights = [original_weights[i] for i in verified_clients]
        
        # 计算POL质量分数
        pol_quality_scores = []
        for i in verified_clients:
            pol_result = pol_results[i]
            client_id = client_ids[i]
            
            # 基础POL分数
            if pol_result['success']:
                # 如果有详细的验证结果，使用成功率
                if 'success_rate' in pol_result:
                    pol_score = pol_result['success_rate']
                else:
                    pol_score = 1.0
            else:
                pol_score = 0.0
            
            # 考虑客户端历史信誉
            reputation = self.client_reputation[client_id]
            if reputation['total_submissions'] > 0:
                historical_success_rate = reputation['pol_success_count'] / reputation['total_submissions']
                # 结合当前验证结果和历史信誉
                pol_score = 0.7 * pol_score + 0.3 * historical_success_rate
            
            pol_quality_scores.append(pol_score)
        
        # 计算最终权重
        final_weights = []
        for i, (orig_weight, pol_score) in enumerate(zip(verified_weights, pol_quality_scores)):
            # 结合原始权重和POL质量分数
            adjusted_weight = (1 - self.pol_weight_factor) * orig_weight + self.pol_weight_factor * pol_score
            final_weights.append(adjusted_weight)
        
        # 归一化权重
        total_weight = sum(final_weights)
        if total_weight > 0:
            final_weights = [w / total_weight for w in final_weights]
        else:
            # 如果总权重为0，使用均匀权重
            final_weights = [1.0 / len(verified_clients)] * len(verified_clients)
        
        return final_weights
    
    def _aggregate_models(self,
                         verified_models: List[nn.Module],
                         weights: List[float],
                         global_model: nn.Module) -> nn.Module:
        """
        执行加权模型聚合
        
        Args:
            verified_models: 通过验证的模型列表
            weights: 聚合权重
            global_model: 全局模型
            
        Returns:
            聚合后的模型
        """
        if not verified_models:
            return global_model
        
        # 创建聚合模型
        aggregated_model = copy.deepcopy(global_model)
        aggregated_state_dict = {}
        
        # 获取第一个模型的状态字典结构
        first_model_state = verified_models[0].state_dict()
        
        # 对每个参数进行加权平均
        for key in first_model_state.keys():
            # 收集所有模型的对应参数
            param_list = []
            for model in verified_models:
                param_list.append(model.state_dict()[key])
            
            # 加权平均
            weighted_param = torch.zeros_like(param_list[0])
            for param, weight in zip(param_list, weights):
                weighted_param += weight * param
            
            aggregated_state_dict[key] = weighted_param
        
        # 加载聚合后的参数
        aggregated_model.load_state_dict(aggregated_state_dict)
        
        return aggregated_model
    
    def get_aggregation_stats(self) -> Dict[str, Any]:
        """获取聚合统计信息"""
        stats = self.aggregation_stats.copy()
        
        if stats['aggregation_times']:
            stats['avg_aggregation_time'] = np.mean(stats['aggregation_times'])
        else:
            stats['avg_aggregation_time'] = 0.0
        
        if stats['verification_times']:
            stats['avg_verification_time'] = np.mean(stats['verification_times'])
        else:
            stats['avg_verification_time'] = 0.0
        
        total_clients = stats['pol_verified_clients'] + stats['pol_rejected_clients']
        if total_clients > 0:
            stats['pol_verification_success_rate'] = stats['pol_verified_clients'] / total_clients
        else:
            stats['pol_verification_success_rate'] = 0.0
        
        return stats
    
    def get_client_reputation(self) -> Dict[int, Dict[str, float]]:
        """获取客户端信誉信息"""
        reputation_summary = {}
        for client_id, rep in self.client_reputation.items():
            if rep['total_submissions'] > 0:
                success_rate = rep['pol_success_count'] / rep['total_submissions']
            else:
                success_rate = 0.0
            
            reputation_summary[client_id] = {
                'success_rate': success_rate,
                'total_submissions': rep['total_submissions'],
                'successful_submissions': rep['pol_success_count']
            }
        
        return reputation_summary
