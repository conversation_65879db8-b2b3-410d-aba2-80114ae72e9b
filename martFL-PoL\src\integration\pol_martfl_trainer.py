"""
POL-martFL Trainer
将POL机制集成到martFL训练过程中
"""

import torch
import torch.nn as nn
import numpy as np
import hashlib
import copy
import time
import sys
import os
from typing import Dict, List, Tuple, Optional, Any

# 添加攻击模拟器路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.join(current_dir, '..', 'attacks'))

from attack_simulator import AttackSimulator


class POLMartFLTrainer:
    """
    POL-martFL集成训练器
    在martFL的训练过程中生成POL证明
    """
    
    def __init__(self,
                 client_id: int,
                 model: nn.Module,
                 device: torch.device,
                 pol_enabled: bool = True,
                 attack_config: Optional[Dict[str, Any]] = None):
        """
        初始化POL-martFL训练器
        按照POL论文设计：k = S (每个epoch保存一次检查点)

        Args:
            client_id: 客户端ID
            model: 模型
            device: 计算设备
            pol_enabled: 是否启用POL机制
            attack_config: 攻击配置（如果为None则为诚实客户端）
        """
        self.client_id = client_id
        self.model = model
        self.device = device
        self.pol_enabled = pol_enabled

        # 攻击模拟器
        if attack_config:
            self.attack_simulator = AttackSimulator(attack_config)
            self.is_malicious = True
        else:
            self.attack_simulator = None
            self.is_malicious = False

        # POL证明组件 (W, I, H, M from POL Algorithm 1)
        self.pol_weights = []      # W: 权重检查点 (每个epoch一次)
        self.pol_indices = []      # I: 数据索引 (每个epoch的所有批次)
        self.pol_hashes = []       # H: 数据哈希 (每个epoch的所有批次)
        self.pol_metadata = {}     # M: 元数据
        self.pol_magnitudes = []   # 权重变化幅度 (用于选择最大更新)

        self.training_step = 0
        self.current_epoch = 0
        self.steps_per_epoch = 0   # S in POL Algorithm 1
    
    def train_with_pol(self,
                      dataloader: torch.utils.data.DataLoader,
                      criterion: nn.Module,
                      optimizer: torch.optim.Optimizer,
                      epochs: int = 1) -> Dict[str, Any]:
        """
        使用POL机制进行训练 - 按POL论文正确实现
        k = S (每个epoch保存一次检查点), Q = 1 (每个epoch验证一个权重变化)

        Args:
            dataloader: 数据加载器
            criterion: 损失函数
            optimizer: 优化器
            epochs: 训练轮数

        Returns:
            训练结果和POL证明
        """
        client_type = "Malicious" if self.is_malicious else "Honest"
        print(f"[Client {self.client_id}] Starting POL training ({client_type})...")

        # 计算每个epoch的步数 (S in POL Algorithm 1)
        self.steps_per_epoch = len(dataloader)

        if self.pol_enabled:
            self._initialize_pol_proof(dataloader, epochs)

        # 如果是有毒数据攻击，修改数据加载器
        if self.attack_simulator and hasattr(self.attack_simulator, 'simulate_poisoned_data_training'):
            if self.attack_simulator.attack_config.get('poison_data', False):
                poison_ratio = self.attack_simulator.attack_config.get('poison_ratio', 0.1)
                dataloader = self.attack_simulator.simulate_poisoned_data_training(dataloader, poison_ratio)
                print(f"  Applied data poisoning attack (ratio: {poison_ratio:.1%})")

        self.model.train()
        total_loss = 0.0
        num_batches = 0

        # POL Algorithm 1: 保存初始权重 W_0
        if self.pol_enabled:
            initial_weights = copy.deepcopy(self.model.state_dict())
            self.pol_weights.append({
                'epoch': 0,
                'weights': initial_weights,
                'is_checkpoint': True
            })
        
        # POL Algorithm 1: 主训练循环 (按epoch组织)
        for epoch in range(epochs):
            self.current_epoch = epoch + 1  # epoch从1开始
            epoch_loss = 0.0
            epoch_batches = 0

            # 记录epoch开始时的权重 (用于计算权重变化幅度)
            epoch_start_weights = copy.deepcopy(self.model.state_dict()) if self.pol_enabled else None

            # POL Algorithm 1: 记录这个epoch的所有数据索引和哈希
            epoch_indices = []
            epoch_hashes = []

            # POL Algorithm 1 Line 6: getBatches(D,S) - 获取这个epoch的所有批次
            dataset_indices = list(range(len(dataloader.dataset)))
            np.random.shuffle(dataset_indices)

            print(f"  Epoch {self.current_epoch}/{epochs}:")

            for batch_idx, (data, target) in enumerate(dataloader):
                data, target = data.to(self.device), target.to(self.device)

                # POL Algorithm 1: 记录数据索引和哈希 (每个批次)
                if self.pol_enabled:
                    start_idx = batch_idx * dataloader.batch_size
                    end_idx = min(start_idx + dataloader.batch_size, len(dataloader.dataset))
                    batch_indices = dataset_indices[start_idx:end_idx]

                    epoch_indices.append(batch_indices)

                    # POL Algorithm 1: H.append(h(D[I_t]))
                    data_hash = self._compute_data_hash(data, target, batch_indices)
                    epoch_hashes.append({
                        'batch': batch_idx,
                        'epoch': self.current_epoch,
                        'hash': data_hash,
                        'batch_size': len(batch_indices)
                    })
                
                # POL Algorithm 1: 执行训练步骤
                optimizer.zero_grad()
                output = self.model(data)
                loss = criterion(output, target)
                loss.backward()

                # 应用拜占庭攻击（修改梯度）
                if self.attack_simulator and self.attack_simulator.byzantine_attack:
                    original_gradients = {}
                    for name, param in self.model.named_parameters():
                        if param.grad is not None:
                            original_gradients[name] = param.grad.clone()

                    attacked_gradients = self.attack_simulator.apply_byzantine_attack(
                        self.model, original_gradients
                    )

                    # 应用攻击后的梯度
                    for name, param in self.model.named_parameters():
                        if name in attacked_gradients:
                            param.grad = attacked_gradients[name]

                # 存储原始权重用于伪造攻击
                if self.attack_simulator:
                    original_weights = {name: param.data.clone() for name, param in self.model.named_parameters()}

                optimizer.step()

                # 应用伪造更新攻击
                if self.attack_simulator and self.attack_simulator.fake_update:
                    attacked_weights = self.attack_simulator.apply_fake_update_attack(
                        self.model, original_weights
                    )

                    # 应用攻击后的权重
                    for name, param in self.model.named_parameters():
                        if name in attacked_weights:
                            param.data = attacked_weights[name]

                # 存储模型用于重放攻击
                if self.attack_simulator and self.training_step % 10 == 0:
                    self.attack_simulator.store_model_for_replay(self.model)

                total_loss += loss.item()
                epoch_loss += loss.item()
                num_batches += 1
                epoch_batches += 1
                self.training_step += 1

            # POL Algorithm 1: Epoch结束，保存检查点和计算权重变化幅度
            if self.pol_enabled:
                # 保存epoch结束时的权重 (k = S, 每个epoch一次检查点)
                epoch_end_weights = copy.deepcopy(self.model.state_dict())
                self.pol_weights.append({
                    'epoch': self.current_epoch,
                    'weights': epoch_end_weights,
                    'is_checkpoint': True
                })

                # 计算这个epoch的权重变化幅度 (用于验证时选择最大更新)
                if epoch_start_weights:
                    magnitude = self._compute_weight_magnitude(epoch_start_weights, epoch_end_weights)
                    self.pol_magnitudes.append({
                        'epoch': self.current_epoch,
                        'magnitude': magnitude
                    })

                # 保存这个epoch的所有数据索引和哈希
                self.pol_indices.append({
                    'epoch': self.current_epoch,
                    'indices': epoch_indices
                })
                self.pol_hashes.append({
                    'epoch': self.current_epoch,
                    'hashes': epoch_hashes
                })

            print(f"    Loss: {epoch_loss / epoch_batches:.6f}")
        
        avg_loss = total_loss / num_batches if num_batches > 0 else 0.0
        
        # 应用模型篡改攻击
        if self.attack_simulator and self.attack_simulator.model_tampering:
            self.model = self.attack_simulator.apply_model_tampering_attack(self.model)
            print(f"  Applied model tampering attack")

        # 保存最终权重
        if self.pol_enabled:
            final_weights = copy.deepcopy(self.model.state_dict())
            self.pol_weights.append({
                'step': self.training_step,
                'epoch': epochs,
                'weights': final_weights
            })
        
        # POL Algorithm 1 Line 17-19: 构建POL证明
        pol_proof = None
        if self.pol_enabled:
            pol_proof = self._construct_pol_proof()
        
        print(f"[Client {self.client_id}] Training completed. Loss: {avg_loss:.6f}")
        if self.pol_enabled:
            weight_checkpoints = len([w for w in self.pol_weights if w['weights'] is not None])
            print(f"  POL proof: {weight_checkpoints} weight checkpoints, {len(self.pol_hashes)} data signatures")

        if self.attack_simulator:
            attack_stats = self.attack_simulator.get_attack_statistics()
            print(f"  Attack statistics: {attack_stats['total_attacks']} attacks executed")
        
        return {
            'model': self.model,
            'pol_proof': pol_proof,
            'loss': avg_loss,
            'training_steps': self.training_step,
            'client_id': self.client_id
        }
    
    def _initialize_pol_proof(self, dataloader: torch.utils.data.DataLoader, epochs: int):
        """初始化POL证明组件"""
        self.pol_weights = []
        self.pol_indices = []
        self.pol_hashes = []
        self.pol_metadata = {
            'client_id': self.client_id,
            'dataset_size': len(dataloader.dataset),
            'batch_size': dataloader.batch_size,
            'epochs': epochs,
            'steps_per_epoch': self.steps_per_epoch,
            'pol_design': 'k=S (epoch-based checkpoints)',
            'model_architecture': str(self.model),
            'timestamp': time.time()
        }
    
    def _compute_data_hash(self, data: torch.Tensor, target: torch.Tensor, indices: List[int]) -> str:
        """
        计算数据哈希 - POL Algorithm 1 Line 11
        
        Args:
            data: 输入数据
            target: 目标标签
            indices: 数据索引
            
        Returns:
            数据哈希字符串
        """
        hash_obj = hashlib.sha256()
        
        # 添加数据内容
        hash_obj.update(data.detach().cpu().numpy().tobytes())
        hash_obj.update(target.detach().cpu().numpy().tobytes())
        
        # 添加索引信息
        for idx in sorted(indices):
            hash_obj.update(str(idx).encode())
        
        # 添加步骤和epoch信息
        hash_obj.update(str(self.training_step).encode())
        hash_obj.update(str(self.current_epoch).encode())
        
        return hash_obj.hexdigest()
    
    def _construct_pol_proof(self) -> Dict[str, Any]:
        """
        构建POL证明 - POL Algorithm 1 Line 17-19
        
        Returns:
            完整的POL证明 R = (W, I, H, M)
        """
        # 更新元数据
        self.pol_metadata.update({
            'total_steps': self.training_step,
            'final_epoch': self.current_epoch,
            'weight_checkpoints_count': len([w for w in self.pol_weights if w['weights'] is not None]),
            'data_signatures_count': len(self.pol_hashes),
            'training_completed': True
        })
        
        # 构建符合POL Algorithm 1的证明
        pol_proof = {
            'W': self.pol_weights,                          # 权重序列 (保持完整结构)
            'I': self.pol_indices,                          # 数据索引
            'H': self.pol_hashes,                          # 数据哈希
            'M': self.pol_metadata,                        # 元数据
        }
        
        return pol_proof
    
    def _compute_weight_magnitude(self, weights1: Dict, weights2: Dict) -> float:
        """
        计算权重变化幅度 - 使用L1距离 (POL Algorithm 1 Line 12)

        Args:
            weights1: 第一组权重 (epoch开始)
            weights2: 第二组权重 (epoch结束)

        Returns:
            权重变化幅度
        """
        total_magnitude = 0.0
        total_params = 0

        for key in weights1:
            if key in weights2:
                w1 = weights1[key].flatten()
                w2 = weights2[key].flatten()

                # POL论文使用L1距离计算magnitude
                magnitude = torch.norm(w2 - w1, p=1).item()
                total_magnitude += magnitude
                total_params += w1.numel()

        return total_magnitude / total_params if total_params > 0 else 0.0

    def get_pol_statistics(self) -> Dict[str, Any]:
        """获取POL统计信息"""
        if not self.pol_enabled:
            return {'pol_enabled': False}

        weight_checkpoints = len([w for w in self.pol_weights if w.get('is_checkpoint', False)])

        stats = {
            'pol_enabled': True,
            'total_steps': self.training_step,
            'epochs_trained': self.current_epoch,
            'weight_checkpoints': weight_checkpoints,
            'data_signatures': len(self.pol_hashes),
            'weight_magnitudes': len(self.pol_magnitudes),
            'steps_per_epoch': self.steps_per_epoch,
            'client_id': self.client_id
        }

        return stats
