4
2
0
2

y
a
M
9

]

R
C
.
s
c
[

4
v
8
9
0
1
0
.
9
0
3
2
:
v
i
X
r
a

martFL: Enabling Utility-Driven Data Marketplace with a Robust
and Verifiable Federated Learning Architecture∗

Qi Li
Tsinghua University & Zhongguancun Laboratory
<EMAIL>

<PERSON><PERSON><PERSON>†
Tsinghua University & Zhongguancun Laboratory
<EMAIL>

Qi Li
Tsinghua University & Zhongguancun Laboratory
<EMAIL>

<PERSON> Xu
Tsinghua University & Zhongguancun Laboratory
<EMAIL>

ABSTRACT
The development of machine learning models requires a large
amount of training data. Data marketplace is a critical platform to
trade high-quality and private-domain data that is not publicly avail-
able on the Internet. However, as data privacy becomes increasingly
important, directly exchanging raw data becomes inappropriate.
Federated Learning (FL) is a distributed machine learning paradigm
that exchanges data utilities (in form of local models or gradients)
among multiple parties without directly sharing the original data.
However, we recognize several key challenges in applying existing
FL architectures to construct a data marketplace. (i) In existing
FL architectures, the Data Acquirer (DA) cannot privately assess
the quality of local models submitted by different Data Providers
(DPs) prior to trading; (ii) The model aggregation protocols in ex-
isting FL designs cannot effectively exclude malicious DPs without
“overfitting” to the DA’s (possibly biased) root dataset; (iii) Prior
FL designs lack a proper billing mechanism to enforce the DA to
fairly allocate the reward according to contributions made by dif-
ferent DPs. To address above challenges, we propose martFL, the
first federated learning architecture that is specifically designed
to enable a secure utility-driven data marketplace. At a high level,
martFL is empowered by two innovative designs: (i) a quality-aware
model aggregation protocol that allows the DA to properly exclude
local-quality or even poisonous local models from the aggregation,
even if the DA’s root dataset is biased; (ii) a verifiable data transac-
tion protocol that enables the DA to prove, both succinctly and in
zero-knowledge, that it has faithfully aggregated these local models
according to the weights that the DA has committed to. This enables
the DPs to unambiguously claim the rewards proportional to their
weights/contributions. We implement a prototype of martFL and
evaluate it extensively over various tasks. The results show that
martFL can improve the model accuracy by up to 25% while saving
up to 64% data acquisition cost.

CCS CONCEPTS
• Computing methodologies → Multi-agent systems; • Security
and privacy → Privacy-preserving protocols;

KEYWORDS
Robust Federated Learning; Data Marketplace; Verifiable Learning

∗This is a longer version of the paper originally published in ACM CCS 2023 [52].
†Zhuotao Liu is the corresponding author.

1 INTRODUCTION
Artificial Intelligence (AI) continues to shape many aspects of our
lives. However, the development of AI models requires a large
amount of high-quality training data. However, collecting data,
especially the private-domain data that is not publicly available on
the Internet, is challenging. The community proposed the concept
of data marketplace [31, 47, 48, 64] to address this problem. In a
data marketplace (such as the International Data Spaces Associ-
ation [6]), organizations can access high-quality data owned by
other organizations that is specific to their needs. However, as data
privacy becomes increasingly important, directly trading raw data
could be inappropriate or even prohibited by laws (e.g., GDPR [72],
PIPL [18]). This implies a fundamental paradigm shift from trading
raw data to only trading data utilities without raw data exchange.
Federated Learning (FL) [62] is a machine learning paradigm that
enables multiple parties to train a global model on their own data
without sharing the data with each other. This is achieved by having
each party train a local model on their own data and then sending
the updates to a central server. The central server then aggregates
the updates from all of the parties to create a global model. This
makes FL a promising paradigm for a utility-driven marketplace
because organizations can buy and sell data without having to
share the underlying data. Viewing FL as a primitive, we could
construct a strawman data marketplace as shown in Figure 1(a).
In this diagram, the aggregation server in FL serves as the data
acquirer (DA) that initiates the FL task. The FL clients serve as the
data providers (DPs) to participate in the FL task by providing local
model updates trained on their own data. The DA evaluates the
local models submitted by different DPs to purchase high-quality
local models. Eventually, the DA aggregates local models to update
the global model, based on which it may initiate another iteration.
However, we recognize three major challenges in applying the
vanilla FL to construct a secure data marketplace. First, the model
aggregation protocol in vanilla FL does not allow the DA to eval-
uate the data quality of local model updates before obtaining the
updates from DPs. This raises a dilemma in data trading: the DPs
are unwilling to give away their local updates before receiving
rewards, while the DA prefers to evaluate the updates first before
purchasing them.

Second, the model aggregation protocol of the vanilla FL is sub-
ject to various attacks, such as [9, 32, 55, 84]. Prior art on miti-
gating these issues can be roughly categorized into client-driven

 
 
 
 
 
 
Figure 1: The architecture comparison between the vanilla FL and martFL.

approaches and server-driven approaches. The client-driven ap-
proaches [12, 77, 78] improve aggregation robustness by smoothing
the local update updates based on their statistics (e.g., median or
average). The server-driven approaches [19, 60] instead rely on
the DA to lead the aggregation process. They assume that the DA
possesses a high-quality root dataset based on which it can calibrate
the local model updates submitted by the DPs. As a result, prior
works exhibit a fundamental tradeoff between inclusiveness and
robustness: the client-driven approaches can potentially include
more local model updates for training, yet they stake robustness
on the “honest majority” (which might be incorrect in the data
trading scenario), while the server-driven approaches are more
resilient against malicious DPs, yet they sacrifice inclusiveness by
“overfitting” to the DA’s existing dataset (which could be biased).
Third, existing model aggregation protocols lack the required
verifiability to enable fair billing. Specifically, in FL, the local mod-
els receiving higher aggregation weights have more impacts on
the final model. Thus, the aggregation weights essentially quan-
tify the values (or utilities) provided by the local model updates.
Prior art (e.g., Omnilytics [54], FPPDL [61]) tries to achieve fair
billing by directly executing the entire model aggregation process
on blockchains, which significantly limits the design space of the
aggregation algorithms (see analysis in § 2.2). Thus, the third chal-
lenge in designing a fair utility-driven data marketplace is to ensure
that the DA faithfully distributes rewards among the DPs according
to their actual aggregation weights. This also ensures that the DA
only pays for its desired model updates, rather than blindly pur-
chasing arbitrary updates, which greatly reduces model acquisition
cost (see evaluation results in § 6.2).

To address these challenges, we present martFL, a novel FL archi-
tecture that enables robust and verifiable local model aggregation
in a utility-driven data marketplace. martFL is powered by two in-
novative designs. First, martFL designs a two-phased protocol that
first privately evaluates all local model updates submitted by DPs
based on a baseline to remove outliers (i.e., local-quality updates)
and then dynamically adjusts the evaluation baseline to incorpo-
rate the high-quality updates. Therefore, our quality-aware model
aggregation protocol eliminates the fundamental tradeoff between
inclusiveness and robustness, by indiscriminately evaluating the
complete set of DPs and meanwhile avoiding overfitting to the
(possibly biased) root dataset owned by DA.

Second, martFL designs a novel verifiable data transaction proto-
col that enables the DA and the selected DPs to securely exchange
the reward and model updates. Our verifiable transaction proto-
col centers around a proving scheme that allows the DA to prove,
both succinctly and in zero-knowledge, that it faithfully aggregates
the model using the committed aggregation weights. Based on the
publicly verifiable proof, the DPs can unambiguously claim the
reward corresponding to their weights. Crucially, martFL achieves
the fair trading without relying on any online trusted third party
to regulate the trading process.
Contributions. The main contribution of this paper is the design,
implementation and evaluation of martFL, the first FL architecture
that simultaneously offers robustness and verifiability to enable a
secure utility-driven data marketplace. We implement a prototype
of martFL in approximately 3750 lines of code and extensively
evaluate its accuracy and robustness using two image classification
datasets and two text classification datasets. The results show that
compared to existing server-driven methods, martFL can improve
accuracy by up to 25% even when the DA has a biased root dataset,
while saving up to 64% data acquisition cost. In addition, martFL can
resist various untargeted attacks, targeted attacks, and Sybil attacks,
and achieves the highest accuracy and the lowest attack success
rate compared to both server-driven and client-driven methods. We
also report the system-level overhead of martFL to demonstrate its
feasibility in practice.

2 BACKGROUND AND MOTIVATION
2.1 Data Marketplace
The traditional circulation of data trading mainly relies on data
trading platforms (such as International Data Spaces [6], BDEX [3],
Quandl [7] and GE Predix [5]) that are endorsed by government
or industry leaders. The research community explored API-based
marketplace designs that allow the data acquirers to collect data
stream online [48]. Due to the rising importance of data privacy,
direct trading of raw data, particularly data associated with personal
information [18, 72], is subject to significant regulatory burdens in
practice. Therefore, it is essential to explore data marketplaces that
do not require direct exchange of raw data.

2

…DP1DP2DPnVanillaDataMarketDAEvaluation &AggregationApp…DP1DP2DPnQuality-Aware Model EvaluationVerifiable TransactionProtocolEvaluateAcceptEvaluateDroppedHierarchicalClusteringEvaluateAcceptPrivateEvaluationBaselineAdjustmentCircuitProofDAAppRewardmartFLReward(a) Vanilla datamarket(b) martFLSmartContractQuantization2.2 Federated Learning and Its Robustness
In designing an AI-specific marketplace, Federated Learning (FL) [62]
is a promising learning paradigm since it enables collaborative train-
ing without directly sharing the raw data. A data marketplace built
upon the vanilla FL architecture has three phases: (i) global model
distribution: the central server (serving as the data acquirer DA)
initializes a global model and distributes it to the clients (serving as
the data providers DPs); (ii) local model training: the DPs use their
local data to train the model and then upload the resulting models
(referred to as local models) to the DA; and (iii) model aggregation:
the DA aggregates these local models to obtain a new global model.
This process repeats for multiple epochs until the DA obtains a
sufficiently accurate global model.

However, the above vanilla FL-driven data marketplace faces
several critical challenges. First, FL is known to be vulnerable to var-
ious attacks, such as untargeted attack [11, 28] (e.g., the Byzantine
clients disrupt the training process by rescaling the sizes of local
gradients or randomizing the directions of local gradients), targeted
attack [9] (e.g., the Byzantine clients mislead the global model to
specifically misclassify certain classes), and Sybil Attack [32]. The
community has therefore proposed various robustness FL designs
that can be roughly divided into two categories. The client-driven
approaches [11, 12, 32, 65, 77, 78] try to exclude malicious local
model updates by learning representative statistics from all local
models; and the server-driven designs [19, 20, 28, 60] instead as-
sume that the server owns a trusted root dataset based on which it
can calibrate these local models. These approaches suffer from a fun-
damental tradeoff between inclusiveness and robustness, resulting
in non-trivial performance degradation (see § 4.1).

In addition to the robustness concern, existing FL architectures
lack several key features that are essential for data trading. On the
one hand, the data acquirer (DA) cannot assess quality of the local
models submitted by different DPs prior to trading; on the other
hand, the DPs are not assured of receiving adequate compensa-
tion after submitting their models. Several recent approaches (e.g.,
Omnilytics [54], FPPDL [61]) try to achieve trading-oriented FL
designs by simply executing the entire model aggregation process on
blockchains, either via general-purpose smart contracts or leverag-
ing specialized block structures. These approaches, however, are
fundamentally limited because they force the DA to make the lo-
cal model assessment protocol publicly executable on blockchains,
preventing the DA from using proprietary and complex/advanced
algorithms. As a result, the aggregation algorithm in FPPDL [61] is
unable to handle malicious DPs; and Omnilytics [54] only supports
four DPs using the simple Secure-Aggregation algorithm [13] with
the multi-Krum [12] algorithm to remove outliers, while incurring
significant gas cost (at least 1000 times more than martFL, as shown
in § 6.2.5). martFL is fundamentally different from these blockchain-
based FL approaches because martFL relies on smart contract to
verify the correctness of the offline model assessment and aggrega-
tion performed by the DA. This enables the DA to design proprietary
and advanced local model evaluation protocols to handle various
FL attacks. Additionally, martFL designs a verifiable transaction
protocol to ensure the DA cannot cheat about the reward allocation,
even though the DA uses proprietary model aggregation protocols
that are not known to the DPs.

2.3 Zero-Knowledge Proofs and Verifiable

Machine Learning

To ensure fair trading, martFL requires the DA to publicly prove
that it has faithfully aggregated local models using the aggregation
weights that were committed to before receiving the plaintext local
models from the DPs. Formally, this proving process can be formu-
lated as an argument of knowledge for the aggregation protocol,
without disclosing these local models to the public. Specifically, an
argument of knowledge for an NP relation R is a protocol between
a prover P and a verifier V. At the end of the protocol, V is con-
vinced by P that there exists a witness 𝑤 such that (𝑥; 𝑤) ∈ R for
some input 𝑥, without disclosing 𝑤. Let G denotes the generation
algorithm that produces the public parameters 𝑝𝑝, we have the
following formal definition.

Definition 2.1. A tuple of algorithms Alg = (G, P, V) is a zero-

knowledge argument of knowledge for R if the following holds.
• Completeness. For every 𝑝𝑝 output by G(1𝜆), (𝑥; 𝑤) ∈ R,
Pr[V (𝑝𝑝, 𝑥, P (𝑝𝑝, 𝑥, 𝑤)) = 1] = 1
• Knowledge Soundness. For any PPT prover P∗, there exists a

PPT extractor E such that 𝑤 ← E P∗ , 𝜋 ← P (𝑝𝑝, 𝑥, 𝑤),

Pr[(𝑥; 𝑤) ∉ R ∧ V (𝑝𝑝, 𝑥, 𝜋) = 1] ≤ negl (𝜆)
where the extractor E P∗ has access to the entire execution, in-
cluding the randomness of P∗.

• Zero-knowledge. There exists a PPT simulator S such that for

any PPT algorithm V∗, it holds that

View(V∗ (𝑝𝑝, 𝑥, 𝜋)) ≈ S V∗

(𝑥)

where View(V∗ (𝑝𝑝, 𝑥, 𝜋)) denotes the view of an honest V∗ in
the interaction with P , S V∗ denotes the view generated by S
that given public coin randomness used by V∗, and ≈ denotes
two perfectly indistinguishable distributions.
We say that Alg = (G, P, V) is a succinct argument system if
the proof size is poly(log |R|, 𝜆) as well as the running time of V
is poly(|𝑥 |, log |R|, 𝜆), where the |R| is the size of the circuit that
computes R as a function of 𝜆.

The advances in zero-knowledge proof technology, especially
the development of zero-knowledge succinct non-interactive ar-
guments of knowledge (zk-SNARK) [15, 16, 33, 40, 45, 73] where
the prover only needs present one message (proof) instead of in-
teracting with the verifier [37], attracted significant attentions.
The machine learning community has conducted significant re-
search in applying verifiable computation to achieve privacy and
fairness in machine learning services, such as verifiable inference
schemes [35, 56, 81] that prove the inference results are produced by
certain models with claimed accuracies, and verifiable training [83]
approaches that prove the traceability of training process.

Yet, simply applying existing zk-SNARK constructions [17, 23,
33, 40] to prove the end-to-end training process in FL is challenging.
This is because (i) the detailed local model evaluation algorithm can
be complex and even contains computations over homomorphically
encrypted values (see § 4.2); and (ii) the model sizes are large, for
instance, with millions of floating-point parameters or even more.
Both of these issues would result in significantly large proof circuits,
which are impractical to implement.

3

2.4 Motivation
To address above challenges, we propose martFL, a secure and ver-
ifiable FL architecture specifically designed for utility-driven data
marketplaces. martFL advances state-of-the-art in both secure local
model aggregation and verifiable data trading. In particular, martFL
designs a novel quality-aware model evaluation protocol that can
indiscriminately and privately assess all the local models submitted
by the DPs based on a dynamically adjusted baseline. As a result,
it can accurately remove malicious local models while avoiding
overfitting to the root dataset owned by the DA, eliminating the
tradeoff between inclusiveness and robustness exhibited in prior
art. Further, martFL proposes an efficient verifiable transaction
protocol that enables fair data trading without the need to prove the
entire FL training process. The key novelty of our approach is that
our proving scheme focuses on only proving the critical compu-
tation that is necessary and sufficient to ensure fair billing. This
results in the proving overhead being independent of both the local
model evaluation algorithm and the model size. Given this proof,
the DPs can unambiguously claim corresponding reward over a
smart contract. To the best of our knowledge, this is the first veri-
fiable scheme designed specifically for proving the correctness of
model aggregation in FL, without directly placing the entire model
aggregation protocol on blockchains.

2.5 Assumptions and Threat Model
We consider Byzantine DPs that may submit arbitrary local models.
They may launch these aforementioned attacks to disrupt the train-
ing process, or try to earn rewards without actual contributions to
training (e.g., the free-rider attack [55]). We consider that the DA
is semi-honest, i.e., the DA is protocol-compliant, but motivated to
manipulate the reward distribution so as to minimize the cost of
collecting data. We assume that the DA possesses a root dataset.
Many well-established robust FL approaches (e.g., [19, 28]) assumed
that DA has a reliable and unbiased root or validation dataset to
handle malicious DPs. In contrast to these approaches, the root
dataset assumed in martFL can be both of poor quality and of limited
volume. For instance, it may contain only half of the labels (i.e.,
the DA’s root dataset exhibits biased distributions), or it may be
approximately 1% of the data held by all DPs (see evaluation results
in § 6.2.1). Therefore, the assumption made about the root dataset
in martFL is significantly less restrictive than that made by exist-
ing robust FL approaches. This makes martFL suitable for the data
trading scenario, in which the DA, without necessarily possessing
a good root dataset, can collect high-quality and high-volume local
models from a diverse set of DPs.

We assume that the cryptographic primitives and the consensus
protocol of the blockchain system used to host the data transaction
smart contract in martFL are secure so that the blockchain can
have the concept of transaction finality and contract publicity. On
Nakamoto consensus based blockchains, finality is achieved by
assuming that the probability of blockchain reorganizations drops
exponentially as new blocks are appended (i.e., the common-prefix
property) [34]. On Byzantine tolerance based blockchains, finality
is guaranteed by signatures from a quorum of permissioned voting
nodes. We assume that the blockchain has a public ledger that
allows external parties to examine the public state of its deployed

(a) Targeted Attack

(b) Untargeted Attack

Figure 2: The tradeoff between robustness and inclusiveness
in prior robust FL approaches.

smart contracts. We assume that the zk-SNARK protocol [40] used
in our verifiable transaction protocol is sound.

3 MARTFL OVERVIEW
Architecturally, martFL is designed around four components (as
shown in Figure 1(b)). (i) A data acquirer (DA) relies on martFL
to collect training data for a FL training task from a utility-driven
marketplace like martFL. Each training epoch is associated with
a reward that the DA will pay after the data trading is closed. (ii)
Data providers (DPs) participate in FL training by contributing their
local model updates. martFL itself has two building blocks. (iii) A
Quality-aware Model Evaluation Protocol that enables the DA to
confidentially pre-evaluate the quality of the local models from
different DPs. The DA can keep the detailed aggregation algorithm
(e.g., how to remove poisonous local models) confidential, making
it difficult for the malicious DPs to manipulate the training process
(see analysis in § 6.3.2). (iv) Afterwards, they apply the Verifiable
Transaction Protocol to achieve fair data trading. The DA first com-
mits the aggregation weights, obtained by the model evaluation
protocol, on the trading smart contract. Upon commitment, the
DPs can safely submit their plaintext local models offline to the DA.
The DA is expected to generate a publicly verifiable proof (with-
out disclosing its model evaluation method and the received local
models) to demonstrate that it has faithfully aggregated these local
models using the committed weights. Given the proof, the DPs can
unambiguously claim the reward (proportional to their aggregation
weights) deposited by the DA on the smart contract. Violations
against the transaction protocol (e.g., the proof verification fails)
results in automatic penalties coded in the smart contract.

4 QUALITY-AWARE MODEL EVALUATIONS
4.1 Key Observations
We first discuss the key observations about the tradeoff between the
inclusiveness and robustness in the prior client-driven and server-
driven secure FL aggregation protocols, which motivates our model
aggregation design. We consider a common data trading scenario
where the DA has an unevenly distributed root dataset prior to
trading, and the data qualities for different DPs vary and some
DPs are malicious. Specifically, using the TREC dataset [53] as an
example, suppose that (i) the root dataset of the DA is dominated by
half of the class labels; (ii) the DPs are heterogeneous, where 30%
of them have high-quality data (evenly distributed across all types
of labels), 30% of them own biased dataset, and 40% of them are
malicious; (iii) the malicious DPs may launch the backdoor attack

4

0.40.50.60.7Inclusiveness0.00.20.40.60.81.0Robustness52.84±1.7683.80±0.8963.88±1.42FLTrustOursKrum0.40.60.81.0Inclusiveness0.60.81.0Robustness54.16±2.6083.80±0.8933.08±7.46FLTrustOursKrum[9] (a type of the targeted attack) or the sign-randomizing attack (a
type of untargeted attack). We evaluate two representative prior
art using this setting: a server-driven design FLTrust [19] and a
client-driven design Krum [12].

We report three metrics in Figure 2. Robustness represents the
ability to exclude poisoned local models, quantified by the per-
centage of malicious DPs whose local models are not selected for
aggregation. Inclusiveness represents the ability to identify benign
DPs, quantified by the percentage of benign DPs whose local mod-
els are selected for aggregation. Accuracy represents the final model
performance on the testset. We observe a clear tradeoff between
inclusiveness and robustness in prior art, where the server-driven
approach has higher robustness while only selecting DPs similar to
the (biased) root dataset (sacrificing inclusiveness), and the client-
driven design behaves the opposite. Instead, our design strikes a
good balance between robustness and inclusiveness, thus yield-
ing significant accuracy gain over prior art. In § 6.3.1, we further
investigate this tradeoff using a series of different parameters.

4.2 Model Aggregation Protocol
The architecture of our local model evaluation protocol is presented
in Algorithm 1. The DA first prepares a baseline model using its own
root dataset (could be biased). This model will be used as a reference
for scoring other local models submitted by DPs in each training
epoch. Afterwards, the DA clusters the DPs according to their scores
and removes the outliers (i.e., low-quality local models) for this
epoch. Finally, the DA and selected DPs finalize the data trading
using our verifiable transaction protocol detailed in § 5, which
guarantees that the DA distributes rewards to the DPs according to
their model quality. Before starting the next training epoch, the DA
dynamically adjusts the baseline to incorporate the high-quality
data collected in the prior epoch, which is the key to address the
possibly biased root dataset. Throughout the training process, we
apply Homomorphic Encryption (HE) to ensure that the DA cannot
obtain plaintext local models before committing to purchase them.

𝑔 by the DA with its root dataset 𝐷0), and 𝑢𝑡

4.2.1 Hierarchical Clustering for Outlier Removal
We score the local models submitted by DPs using cosine similarities
(similar to FLTrust [19]). Suppose that 𝑊 𝑡
𝑔 is the global model at
round 𝑡, 𝑊 𝑡 ′
𝑔 is the baseline model (in the first epoch, it trained from
𝑊 𝑡
𝑔 −𝑊 𝑡
𝑔 )
is therefore the self-update computed by the DA. Suppose that 𝑊 𝑡
𝑖
is the model obtained by the 𝑖-th DP after it trains 𝑊 𝑡
𝑔 on its local
𝑖 = Flatten(𝑊 𝑡
dataset 𝐷𝑖 , and 𝑢𝑡
𝑔 ) is the update computed by
the 𝑖-th DP. Then, the score of 𝑢𝑡

𝑖 − 𝑊 𝑡
𝑖 is calculated as follows.

𝑔 = Flatten(𝑊 𝑡 ′

𝑖 = Cosine(𝑢𝑡
𝑠𝑡

𝑔, 𝑢𝑡

𝑖 ) =

𝑢𝑡
𝑔 · 𝑢𝑡
𝑖
𝑔 || · ||𝑢𝑡
||𝑢𝑡
𝑖 ||

(1)

The DA selects the desired updates according to their scores. Unlike
the FLTrust [19] that simply clips the scores via ReLU, our design
avoids simply referencing the DA’s root dataset by analyzing the
cluster distribution of all scores. Specifically, we propose a hierar-
chical clustering algorithm to select the desired updates. We first
apply the Gap-Statistics algorithm [70] to determine the optimal

Algorithm 1: Quality-Aware Model Aggregation Protocol
1 Inputs: The scores of local models in the 𝑡 -th training epoch

1, 𝑠𝑡

2, . . . , 𝑠𝑡

𝑛 }; the DP selected as the baseline in the 𝑡 -th

S𝑡 = {𝑠𝑡
epoch 𝑝𝑡 ; a control flag 𝛼 for baseline adjustment; the ratio of
randomly selected baseline candidates 𝛽; the threshold 𝑇 used in
hierarchical clustering; the root dataset 𝐷0; the maximum number
of clusters 𝐺.

2 Outputs: The aggregation weights obtained for the 𝑡 -th epoch and
the DP selected as the baseline for the (𝑡 + 1)-th epoch 𝑝𝑡 +1.

// Set P𝑡 stores the DPs selected for aggregation; Set K𝑡 stores their weights.

// M𝑡 are the plaintext models that the DA commits to purchase.

3
4 Function Main ( S𝑡 , 𝑝𝑡 , 𝛼, 𝛽,𝑇 , 𝐷0, 𝐺 ) :
5
6 P𝑡 , K𝑡 ← OutlierRemoval ( S𝑡 , 𝑝𝑡 , 𝛽,𝑇 , 𝐺 )
7
8 M𝑡 ← ModelTrading( P𝑡 )
9 if 𝛼 = true then 𝑝𝑡 +1 ← BaselineAdjustment( M𝑡 , 𝐷0 )
10 else 𝑝𝑡 +1 ← 0
11
12 Function OutlierRemoval( S𝑡 , 𝑝𝑡 , 𝛽,𝑇 , 𝐺 ) :
13 U ← {1, 2, . . . , 𝑛}, P1 ← ∅, P2 ← ∅, K ← {1.0, . . . , 1.0}
14 // Determine the number of clusters ˆ𝑔 by Gap statistics.
15 for 𝑔 ← 1, 2, . . . , 𝐺 do
16
17 𝑑 ← Max( S𝑡 ) −Min( S𝑡 )
18 if ˆ𝑔 = 1 and 𝑑 > 𝑇 then ˆ𝑔 ← 2
19 else P1 ← U // Single-cluster gathered distribution.

// K-Means returns the clusters and centroids of the scores.

20
21 N1, C1 ← K-Means( S𝑡 , ˆ𝑔)
22 𝐶𝑏𝑒𝑠𝑡 = Max( C1 ) // Centroid of the highest-score cluster.
23 if ˆ𝑔 > 2 then N2, C2 ← K-Means( S𝑡 , 2) // Re-clustering.
24 else N2 ← N1
25 for 𝑖 ← 1, 2, . . . , 𝑛 do
26

if ˆ𝑔 = 1 then break
if 𝑖 = 𝑝𝑡 or N1 [𝑖 ] = 0 or N2 [𝑖 ] = 0 then
K [𝑖 ] ← 0.0 // Low-quality model.

else if N1 [𝑖 ] = ˆ𝑔 − 1 and (N2 [𝑖 ] ≠ 0 ) then

K [𝑖 ] ← 1.0, P1.add(𝑖) // High-quality model.

27

28

29

30

31 else

32

K [𝑖 ] ← 1.0 −

Abs(S [𝑖 ]−𝐶𝑏𝑒𝑠𝑡 )
𝑖 −𝐶𝑏𝑒𝑠𝑡 for 𝑠𝑡
P2.add(𝑖) // Qualified but weighted model.

Max(Abs( [𝑠𝑡

33
34 if P2 = ∅ and Len( P1 ) < 0.5 × 𝑛 then
P2 ← RandomSample( U − P1, 𝛽 )
35
36 return P1 (cid:208) P2,
K
Sum(K )

𝑖 in S ]) )

ˆ𝑔 ← the minimum g such that Gap(𝑔) − Gap(𝑔 + 1) +𝜎𝑔+1 ≥ 0

37
38 Function BaselineAdjustment( M𝑡 , 𝐷0 ) :
39 𝑘𝑝𝑚𝑎𝑥 = -inf, 𝑝𝑡 +1 = 0
40 for 𝑖, 𝑚 in Enumerate( M𝑡 ) do
𝑘𝑝 ← Kappa(𝑚, 𝐷0 )
41
if 𝑘𝑝 > 𝑘𝑝𝑚𝑎𝑥 then 𝑘𝑝𝑚𝑎𝑥 ← 𝑘𝑝, 𝑝𝑡 +1 ← 𝑖

42
43 return 𝑝𝑡 +1

number of clusters ˆ𝑔 1. Afterwards, we obtain our first-layer clus-
tering by applying the K-Means algorithm [59] with ˆ𝑔. This may
produce three types of distributions, as shown in Figure 3.

1The elbow coefficient [69] and silhouette [68] are other possible methods to calculate
ˆ𝑔. However, the elbow coefficient algorithm requires manual judgment to determine

5

4.2.2 Dynamic Baseline Adjustment
To avoid overfitting to the DA’s root dataset, martFL enables the DA
to dynamically adjust the baseline for outlier removal. Specifically,
for each local model 𝑚 ∈ M𝑡 , the DA evaluates 𝑚 on its root dataset
and computes the Kappa coefficient [24]. The DA then selects the
DPs with high Kappa coefficients as preferred DPs. For simplicity,
Algorithm 1 only selects the DP with the highest-Kappa-coefficient
as the single preferred DP (line 42). In the next epoch, the DA
trades the local models in advance with these preferred DPs and
aggregates them as the new baseline. The DA should not disclose
these preferred DPs until they have committed their local models.

4.3 The Integrated Training Process
We have described the local model evaluation protocol in martFL.
In a utility-driven data marketplace, it is critical to ensure that the
DA cannot obtain the plaintext model updates before committing
to purchase them. Towards this end, we apply the CKKS Homo-
morphic Encryption [22] to allow DA to privately assess the local
models submitted by the DPs.

update 𝑢𝑡

Supposed that in the 𝑡-th epoch, the DA obtains the baseline
𝑔 with the DPs, the DA ho-
𝑢𝑡
𝑔
).
𝑔 = Enc(𝑘,
| |𝑢𝑡
𝑔 | |
𝑔, it multiply its local update 𝑢𝑡
𝑖 with 𝑐𝑡
𝑔 as
𝑔, and returns the result back to the DA. Eventually,
𝑖 , and decrypts it to obtain
𝑖 . Afterwards, the DA can perform local

𝑔. Instead of directly sharing 𝑢𝑡
momorphically encrypts it by the public key 𝑘 as 𝑐𝑡
Once a DP receives 𝑐𝑡
𝑐𝑡
· 𝑐𝑡
𝑖 =
the DA receives the encrypted cosine 𝑐𝑡
the score for the update 𝑢𝑡
model evaluations as described in § 4.2.

𝑢𝑡
𝑖
| |𝑢𝑡
𝑖 | |

A critical step in adopting CKKS is to safeguard against the DPs
from using different models in model evaluation and subsequent
model transactions, i.e., preventing the DPs from intentionally sub-
mitting different model updates after being selected by the DA.
To this end, we require the DPs to commit their model updates
before model evaluations. These committed updates are then used
to ensure the correctness of subsequent model transactions, as we
will further discuss below.

5 VERIFIABLE TRANSACTION PROTOCOL
Our verifiable transaction protocol has two phases: (i) a zero-knowledge
proving system that allows the DA to prove that it has faithfully
aggregated the global model based on claimed weights, without
disclosing the local models submitted by the DPs; and (ii) a pay-
ment protocol based on smart contract to allow the DA and DPs to
exchange rewards and plaintext local models.

5.1 Proving Scheme for Model Aggregation
5.1.1 Overview
The DA should prove that it faithfully aggregates the global model.
Although there are many zero-knowledge proof (ZKP) construc-
tions [2, 4, 27], it is challenging to simply adopt these designs to
achieve verifiable aggregation in martFL. Specifically, the model
evaluation algorithm (Algorithm 1) used by martFL is complex, es-
pecially considering the homomorphic computations involved. This
complexity makes it difficult to generate and implement the arith-
metic circuit to represent the algorithm. To address this challenge,

Figure 3: Three different cases of distribution of scores.

• Single-cluster gathered distribution: the model scores are concen-
trated, and the range of scores is less than a predefined threshold
𝑇 . This often indicates models submitted by all DPs have compara-
ble quality. In this case, we include all updates to the high-quality
model set P1 (line 19 of Algorithm 1).

• Single-cluster scattered distribution: the model scores are scat-
tered over a large range. This could be a sign of attack, where the
malicious DPs intentionally submit arbitrary model updates. As
a result, we perform the second layer of clustering (via K-Means
clustering with ˆ𝑔=2) to divide the scores into a high-quality clus-
ter and a low-quality cluster. The updates in the high-quality
cluster are selected for aggregation in this round (line 30 in Al-
gorithm 1) .

• Multi-cluster distribution: the update scores form multiple clus-
ters. This could be caused by highly-heterogeneous DPs where
some of them possess good dataset, some of them possess biased
dataset and some of them are malicious. The algorithm performs
the second clustering by separating these first-stage clusters into
two categories. The low-quality category is discarded. Within
the high-quality category, the updates in the highest-score clus-
ter are added to the set P1. The local models in the remaining
clusters of the high-quality category considered to be qualified
(line 32 of Algorithm 1), but weighted based on their distances
to the centroid of the highest-score cluster.

Eventually, the DPs in P1 and a small subset of DPs (e.g., 5%-10%)
randomly selected from P2 are selected for aggregation. The DA
first commits to purchase local models from these DPs. Afterwards,
it is safe for the selected DPs to hand over the plaintext local models
to the DA (see the detailed transaction protocol in § 5).

the position of the elbow, and the silhouette algorithm can only be used with two or
more clusters.

6

Scores gathered in a small range.𝑑<T	𝐾	=	1𝐾>	1𝑑>T	Re-clusteringSelectedDroppedScores scattered in a large range.Re-clusteringRemain UnchangedSelectedAllDroppedSelectedHigh-qualityLow-qualityOther qualified models.the quantization and de-quantization are represented as 𝑞 = ⌊ 𝑥
𝑠 ⌋ +𝑧
and 𝑥 = 𝑠 (𝑞 − 𝑧), respectively, where 𝑠 is a floating-point scaler, 𝑞
is the quantized integer for 𝑥, and 𝑧 is the zero point (i.e., the value
of a floating-point zero when mapped to the integer field).

We observe in our experiments that the above quantization de-
sign may result in overflow. Specifically, the subtractions on un-
signed integers may result in overflow due to accuracy loss in
quantization. Consequently, the de-quantization produces a very
inaccurate dequantized model for the next training epoch. To avoid
overflow, we extend the range of floating-point numbers by a small
𝜖, i.e., 𝑥 ∈ [𝑎 − 𝜖, 𝑏 + 𝜖]. Afterwards, we derive 𝑠 and 𝑧, by solving
the following linear Equation (2).

𝑎 − 𝜖 = 𝑠 (𝑎𝑞 + 𝑧);

𝑏 + 𝜖 = 𝑠 (𝑏𝑞 + 𝑧)

(2)

Commitment Circuit. The first part of C is to commit the pri-
vate parameters U𝑡 with the opening random 𝑟𝑡 such that U𝑡 are
not disclosed to the public verifiers, i.e., 𝑐𝑚𝑡 = Commit(U𝑡 , 𝑟𝑡 ).
POSEIDON [39] is an optimized commitment algorithm. Yet, sim-
ply applying POSEIDON to commit all model parameters would
require in a large number of constraints. In § 5.1.3, we design a
verifiable sampling mechanism to avoid committing and verifying
all parameters.
Aggregation Circuit Design. The second part of C is the aggre-
gation circuit that computes 𝑈 𝑡 ′
= 𝐾𝑡𝑈 𝑡 in the quantized form,
where 𝐾𝑡 ∈ R1×𝑛, 𝑈 𝑡 ∈ R𝑛×𝑚, 𝑈 𝑡 ′
∈ R1×𝑚, 𝑛 is the number of
DPs, and 𝑚 is the number of parameters in the model. To be ZKP-
friendly, we minimize the use of negative numbers and division in
the calculation, while ensuring that all operations are performed
in the field F𝑞. First, in Equation (3), we apply the de-quantization
equation.

𝑈 𝑠 ′

(U𝑞′

𝑖,𝑗 − 𝑈 𝑧′

) =

𝑛
∑︁

𝑘=1

𝐾𝑠 (K𝑞

𝑖,𝑘 − 𝐾𝑧)𝑈 𝑠 (U𝑞

𝑘,𝑗 − 𝑈 𝑧)

(3)

where K𝑞, U𝑞 and U𝑞′ are the quantization matrix of 𝐾𝑡 , 𝑈 𝑡 , and
𝑈 𝑡 ′ , respectively; 𝐾𝑠 , 𝑈 𝑠 and 𝑈 𝑠 ′ are the scaler of 𝐾𝑡 , 𝑈 𝑡 , and 𝑈 𝑡 ′ ,
respectively; 𝐾𝑧, 𝑈 𝑧 and 𝑈 𝑧′ are the zero points of 𝐾𝑡 , 𝑈 𝑡 , and 𝑈 𝑡 ′ ,
respectively. In the context, K𝑡 = {K𝑞, 𝐾𝑠, 𝐾𝑧 }, etc. We use a big
integer 2𝜂 (𝜂 should be 22 or even larger) to replace the floating-
point scale with unsigned integers and enable the full quantization
computation. Also, we rearrange the calculation order in Equation
(4) to eliminate negative numbers in calculation. The remainder R𝑎
is to ensure correctness after division, as shown in [30].

2𝜂 U𝑞′

𝑖,𝑗 + 2𝜂𝑈 𝑧′
𝑖,𝑗 = R𝑎
𝑛
∑︁

K𝑞
𝑖,𝑘

U𝑞
𝑘,𝑗

s.t. 𝑀1 =

𝑘=1

(cid:18)

(cid:22)

2𝜂 𝐾𝑠𝑈 𝑠
𝑈 𝑠 ′

+

𝑀1 + 𝑀4 − 𝑀2 − 𝑀3

(cid:19)(cid:23)

, 𝑀2 = 𝑈 𝑧

𝑛
∑︁

𝑘=1

K𝑞
𝑖,𝑘

,

(4)

𝑀3 = 𝐾𝑧

𝑛
∑︁

𝑘=1

U𝑞
𝑘,𝑗

, 𝑀4 = 𝑛𝐾𝑧𝑈 𝑧

Update Circuit Design. The third part of C is the update circuit.
We use Equation (5) to present the de-quantized update equation
∈ R1×𝑚.
𝑔 ∈ R1×𝑚, 𝑊 𝑡 −1
𝑊 𝑡

+ 𝑈 𝑡 ′ , with 𝑊 𝑡

∈ R1×𝑚, 𝑈 𝑡 ′

𝑔 = 𝑊 𝑡 −1
𝑔

𝑔

Figure 4: The circuit design for the proving scheme in martFL.

martFL does not prove the end-to-end training process. Instead, it
only proves the local model summation computation, which ag-
gregates the local models using the aggregation weights returned
by the model evaluation algorithm. This design drastically reduces
the proving complexity without affecting the fairness of billing, be-
cause reward allocations are completely driven by the aggregation
weights. In addition, we also design verifiable sampling method such
that the DA only needs to prove a fix number of scalars/parameters
regardless of the model size (i.e., the number of model parameters).
Setup. Denote the local model summation as A, which represents
+ 𝐾𝑡𝑈 𝑡 , where 𝑊 𝑡 −1
𝑔 = 𝑊 𝑡 −1
(𝑊 𝑡
the following calculation 𝑊 𝑡
𝑔 ) is
𝑔
the global model in the previous (current) epoch. For each 𝑘𝑡
𝑖 ∈ K𝑡 ,
𝐾𝑡 = [𝑘𝑡
2, . . . , 𝑘𝑡
1, 𝑘𝑡
𝑛] are the aggregation weights claimed by the
DA, and 𝑈 𝑡 = [𝑢𝑡
1, 𝑢𝑡
𝑛] are the local models submitted by
the DPs. The public input of our zero-knowledge proving scheme
is X𝑡 = {𝑊 𝑡
, 𝐾𝑡 }, and the private witness is W𝑡 = {𝑈 𝑡 }.
Concretely, our proving scheme has the following algorithms.
• C ← Compile(A): In the compiling step, the prover (i.e., the DA)
quantizes the floating-point public input X𝑡 and private witness
, K𝑡 } and W𝑡 = {U𝑡 } in finite field,
W𝑡 to X𝑡 = {W𝑡
respectively. In addition, it quantizes the aggregation algorithm
A and compiles it to a circuit C.

2, . . . , 𝑢𝑡

𝑔 ,𝑊 𝑡 −1
𝑔

𝑔, W𝑡 −1
𝑔

𝑔

• (𝑝𝑘, 𝑣𝑘) ←Setup(1𝜆, C): Given a security parameter 𝜆 and the
circuit C, a trusted third party randomly generates a proving key
𝑝𝑘 and a verification key 𝑣𝑘. The proving key 𝑝𝑘 is given to the
DA and the verification key 𝑣𝑘 is given to the DPs. We consider
a proving scheme that requires trusted setup in this paper, and
leave exploration of trust-free schemes in future work.

• (𝑐𝑚𝑡 , W𝑡

𝑔, 𝜋𝑡 ) ← Prove(X𝑡 , W𝑡 , 𝑟𝑡 , 𝑝𝑘, C): Given a random open-
ing 𝑟𝑡 , the prover first commits the private witness W𝑡 as 𝑐𝑚𝑡 =
Commit(U𝑡 , 𝑟𝑡 ). Then it calculates the quantized global model
W𝑡
𝑔 and generates a proof 𝜋𝑡 . Afterwards, the prover publishes
𝑐𝑚𝑡 , 𝜋𝑡 , and W𝑡

𝑔 to the DPs.

• {1, 0} ← Verify(X𝑡 , 𝑣𝑘, 𝜋𝑡 , 𝑐𝑚𝑡 ): The public verifiers (e.g., DPs)
can verify the computation in C using the verification key 𝑣𝑘,
public input X𝑡 , the commitment 𝑐𝑚𝑡 , and the proof 𝜋𝑡 . If the
DA faithfully aggregates the global model, the verifier will accept
the proof; otherwise, the verifier will reject it.

5.1.2 Circuit Design
Quantization The circuit C is designed based on the quantized
version of algorithm A. Quantization maps a floating point value
𝑥 ∈ [𝑎, 𝑏] to an unsigned integer 𝑥𝑞 ∈ [𝑎𝑞, 𝑏𝑞] and de-quantization
is the reversed process. As defined in the partial quantization [41],

7

𝑊!"𝑊!"#$𝐾"𝑈"+=𝕎%𝑊&𝑊’𝕂%𝐾&𝐾’𝕎%(𝑊&(𝑊’(𝕌%𝑈&𝑈’2)2)UpdateCircuitAggregateCircuitCommitCircuit𝕌%(Quantization𝑊 𝑠 ′

(W𝑞′

𝑖,𝑗 − 𝑊 𝑧′

) = 𝑊 𝑠 (W𝑞

𝑖,𝑗 − 𝑊 𝑧) + 𝑈 𝑠 ′

(U𝑞′

𝑖,𝑗 − 𝑈 𝑧′

),

(5)

where W𝑞′ , W𝑞 and U𝑞′ are the quantization matrices of 𝑊 𝑡
𝑔 , 𝑊 𝑡 −1
𝑔
and 𝑈 𝑡 ′ , respectively; 𝑊 𝑠 ′ , 𝑊 𝑠 and 𝑈 𝑠 ′ are the scaler of 𝑊 𝑡
𝑔 , 𝑊 𝑡 −1
𝑔
and 𝑈 𝑡 ′ , respectively. 𝑊 𝑧′ , 𝑊 𝑧 and 𝑈 𝑧′ are the zero points of 𝑊 𝑡
𝑔 ,
𝑊 𝑡 −1
𝑔
Similarly, we rearrange the above equation to Equation (6) to
eliminate negative numbers. And the remainder R𝑢 to ensure cor-
rectness after division.

and 𝑈 𝑡 ′ , respectively.

2𝜂 W𝑞′

s.t. 𝑁1 =

𝑖,𝑗 + 2𝜂𝑊 𝑧′
𝑖,𝑗 = R𝑢
𝑊 𝑠
𝑊 𝑠 ′
𝑈 𝑠 ′
𝑊 𝑠 ′

U𝑞′

W𝑞

𝑁3 =

𝑖,𝑗 , 𝑁4 =

𝑖,𝑗 , 𝑁2 =

(cid:22)

2𝜂 (cid:18)

+

𝑁1 + 𝑁3 − 𝑁2 − 𝑁4

(cid:19) (cid:23)

𝑊 𝑠
𝑊 𝑠 ′ 𝑊 𝑧,
𝑈 𝑠 ′
𝑊 𝑠 ′ 𝑈 𝑧′

(6)

In summary, the complete circuit C is plotted in Figure 4.

5.1.3 Verifiable Sampling
Given the concatenated local models 𝑈 𝑡 ∈ R𝑛×𝑚 (where 𝑛 is the
number of DPs, and 𝑚 is the number of parameters in the model),
the number of constraints required in the commitment circuit, the
aggregation circuit, and the update circuit is O (𝐻 ·𝑛·𝑚), O (𝑛·𝑚) and
O (𝑚), respectively, where 𝐻 represents the required constraints
in the hash function used in commitment circuit. Considering that
𝑛 ≪ 𝑚 and 𝐻 is fixed once the commitment hash function is
selected, we explore to reduce the number of parameters required
for proof generation. Specifically, we randomly select 𝑐 out of 𝑚
parameters as the verification objects. Suppose that the sampling
is provable random (i.e., not controlled by the DA), as long as the
DA can provide the correct proof for the sampled parameters, then
with high probability, the DA has calculated all parameters correctly.
Thus, the proof complexity becomes independent on 𝑚.

Conceptually, the provable random sampling is similar to ran-
domness beacon [67]. Both verifiable random function (VRF) [38,
63] and verifiable delay function (VDF) [14] can be used as a primi-
tive to construct the verifiable random sampling. We sketch a con-
struction below. In each training epoch, each DP publishes a cryp-
tographic nonce to a public bulletin board (e.g., a public blockchain).
The DA is required to use H (𝑠1, 𝑠2, ..., 𝑠𝑛) as the seed 𝑠𝑡
to a VDF
to select the parameter indices 𝑅𝑡
𝑐 } (e.g., using the
vdf
output of the VDF as the random seed for a pre-agreed pseudoran-
dom number generator). VDF is necessary to prevent the DP that
lastly publishes its nonce from introducing bias by strategically
selecting its nonce.

2, . . . , 𝑟 𝑡

= {𝑟 𝑡

1, 𝑟𝑡

vdf

, W𝑡

, . . . , W𝑡

After random sampling, the public input and private witness for
𝑔 =
}, U𝑡,𝑐 =

the proving scheme should be also adjusted accordingly as W𝑡,𝑐
, . . . , W𝑡 −1
{W𝑡
𝑔,𝑟 𝑡
𝑐
∈ R1×𝑐 , U𝑡,𝑐 ∈
{𝑢𝑡
𝑟 𝑡
1
R𝑛×𝑐 . As a prerequisite for using the sample-based verification, the
DA shall publish the model W𝑡
𝑔 before sampling (since only part of
the W𝑡

}, W𝑡 −1,𝑐
𝑔
𝑔,𝑟 𝑡
𝑐
}, where W𝑡,𝑐
𝑔

= {W𝑡 −1
, W𝑡 −1
𝑔,𝑟 𝑡
𝑔,𝑟 𝑡
2
1
∈ R1×𝑐 , W𝑡 −1,𝑐

𝑔,𝑟 𝑡
2
, . . . , 𝑢𝑡
𝑟 𝑡
𝑐

𝑔 is used as the public input).

𝑔,𝑟 𝑡
1
, 𝑢𝑡
𝑟 𝑡
2

𝑔

Algorithm 2: The Trading Smart Contract
1 PreparePhase() :
2

commit 𝐾𝑡 , addrs # addrs identify selected DPs in current epoch
𝑣DPs, 𝑣DA = Deposit(msg.value) # DA deposits (reward, penalty)
𝑅DPs := Allocate(𝑣DPs, 𝐾𝑡 ) # allocate DPs reward based on 𝐾𝑡
𝑈 𝑡 := Submission(addrs) # DPs submit local models off-chain

𝑔 , K𝑡 , U𝑡 ) # DA generates proof off-chain

𝑔 , K𝑡 # DA commits public inputs

𝑔 := Aggregate(W𝑡 −1
𝑔, W𝑡 −1

5
6 W𝑡

commit W𝑡
7
8 VerifyPhase() :
9

𝑔 , W𝑡 −1,𝑐
and U𝑡,𝑐 based on 𝑅𝑡
𝑔
:=Prove(X𝑡,𝑐, W𝑡,𝑐, 𝑝𝑘, C) offline

DA performs verifiable sampling offline and publishes 𝑠𝑡
vdf
DA adjusts W𝑡,𝑐
DA generates 𝜋𝑡
DA publishes the proof 𝜋𝑡
DPs invokes verification 𝑣𝑡 := Verify(𝑣𝑘, X𝑡,𝑐 , 𝜋𝑡
if 𝑣𝑡 = false :

on-chain

agg

agg

agg

vdf

)

, 𝜋𝑡

vdf

distribute both the security deposit 𝑣DA the award 𝑣DPs to DPs

else : distribute 𝑣DPs to DPs and return 𝑣DA to DA

3

4

10

11

12

13

14

15

) :

18

agg

16
17 Function Verify(𝑣𝑘, X𝑡,𝑐, 𝜋𝑡
𝑠 := ΣLen(X𝑡,𝑐 ) −1
𝑖=0
𝑠 :=Addition(𝑠, vk.𝛾𝑎𝑏𝑐 [0])
𝑝1 := 𝜋𝑡
𝑝2 := 𝜋𝑡
.b, vk.𝛾 , vk.𝛿, vk.𝛽
return PairingCheck(𝑝1,𝑝2)

agg

20

19

21

22

ScalarMul(vk.𝛾𝑎𝑏𝑐 [i + 1],X𝑡,𝑐 [i])

𝑎𝑔𝑔.a, Negate(𝑠), Negate(𝜋𝑡

.c), Negate(vk.𝛼)

agg

𝑔

Integrated Verification Protocol

5.1.4
Taken all parts together, our verifiable aggregation protocol pro-
ceeds as follows. The DA first quantizes 𝑊 𝑡 −1
, 𝑈 𝑡 and 𝐾𝑡 to the
quantized format, and performs the aggregation calculation as
Equation (4) and Equation (6). In addition, the DA applies the de-
quantization equation to calculate the floating-point global model
𝑊 𝑡
𝑔 to DPs. Afterwards, the DA ob-
tains the randomly selected parameters from the VDF, and gen-
erates a zero-knowledge proof 𝜋𝑡 with public input as X𝑡,𝑐 =
{W𝑡,𝑐
, K𝑡 } and private witness as W𝑡,𝑐 = {U𝑡,𝑐 }. The proof
𝜋𝑡 is then submitted to a smart contract so that the DPs can verify
its correctness and claim corresponding rewards (see § 5.2).

𝑔 , and commits 𝐾𝑡 ,𝑊 𝑡

𝑔 , W𝑡 −1,𝑐
𝑔

𝑔 and W𝑡

5.2 The Trading Smart Contract
martFL designs a trading smart contract to enable the DA and DPs
to exchange plaintext local models and rewards. Due to space con-
straint, we provide the high-level description of our smart contract
in Algorithm 2. The more detailed realization of our smart contract
that is close to the real-world implementation is deferred to § A.1.
The trading smart contract is divided into two high-level phases.
Prepare Phase. The PreparePhase performs necessary setup for
reward distribution. First, the DA commits the aggregation weights
𝐾𝑡 and the corresponding DPs (identified by their public keys or
addresses on blockchain) in the smart contract. Meanwhile, the DA
deposits the reward 𝑣DPs for the DPs proportional to their weights
in 𝐾𝑡 . Additionally, the DA also deposits 𝑣DA as the penalty if it
cannot later provide a correct proof. Afterwards, the DPs can safely
submit their plaintext local models off-chain to the DA, based on
which the DA generates the verifiable proof as described in § 5.1.
After proof generation, the DA commits the public inputs.

8

vdf

vdf

agg

Verify Phase. The second phase focuses on verifying the integrity
of model aggregation. The DA performs verifiable random sam-
pling and provides the proper proof (i.e., 𝜋𝑡
) for randomness.
Afterwards, the DA adjusts the public and private inputs according
to the random seed 𝑠𝑡
, based on which it generates the final proof
for model aggregation 𝜋𝑡
. The proof is uploaded to the trading
smart contract such that any DP can verify its correctness by in-
voking the on-chain Verify function. The DA will lose its security
deposit if the verification fails.
On-Chain Verification Procedure. The Verify function is respon-
sible for checking the correctness of 𝜋𝑡
. It takes input as the com-
mitted verification key 𝑣𝑘 and the quantized public input X𝑡,𝑐 , and
the proof 𝜋𝑡
. The underlying verification is based on the Groth16
protocol [40] which checks four pairings. The cryptography-related
computations (such as Addition and ScalarMul) are implemented
via the precompiled smart contracts to reduce gas cost.

agg

agg

6 EVALUATION
6.1 Experimental Setup
Our experiments are conducted on two Linux servers with Intel(R)
Xeon(R) Gold 6348 CPU and NVIDIA RTX A100 GPU. We use
Pytorch [66] to implement FL, apply SEAL [1] for CKKS-based
Homomorphic operations, and Ethereum [74] testnet for deploying
our trading smart contract. The source code is available at Github2.
All results are obtained based on five repetitions of experiments.
Datasets, Models, and Baselines. We use multiple datasets from
different domains in our evaluations, including two image classifi-
cation datasets, FMNIST [75] and CIFAR [49], and two text classifi-
cation datasets, TREC [53] and AGNEWS [82]. We train LeNet [50]
as global model for FMNIST [75], TextCNN [79] for TREC [53] and
AGNEWS [82]. We train a convolutional neural network (CNN)
with three CNN layers and four linear layers as the global model for
the CIFAR [49] dataset. The architecture of the CNN is shown in
Table 8 of § A.2. We compare martFL with two server-driven meth-
ods (FLTrust [19] and CFFL [60]) and five client-driven methods
(FedAvg [62], RFFL [77], Krum [12], RLR [65], and Median [78]).
Training. We set the same number of participants for both client-
driven and server-driven approaches. This ensures that all partici-
pants use the same number of samples in the training process. For
client-driven methods, we set 𝑛 DPs. For server-driven methods,
we set one DA and 𝑛 − 1 DPs. For image classification tasks, we
set 30 participants, the optimizer is SGD, and the learning rate is
1.0 × 10−2. For text classification tasks, we set 20 participants, the
optimizer is Adam, and the learning rate is 5.0 × 10−5. The number
of samples in the DA’s root dataset is 200 for FMNIST, CIFAR, and
AGNEWS, and 120 for TREC, counting for roughly 0.3%, 0.4%, 2%,
and 1.6% of the total data held by the DPs, respectively. Unless
otherwise specified, we train a model until its peak accuracy on
our validation dataset does not increase for 100 training epochs.
Data Splits. We apply two sampling methods to divide the amount
of data held by each DP: UNI and POW. In UNI, each DP has the
same amount of samples; in POW method, the numbers of samples
owned by different DPs follow a power-law distribution. In addition,
we divide the local data distribution of the DPs according to two

2https://github.com/liqi16/martFL

9

methods, IID and NonIID. IID means that each DP has all classes of
samples and the samples in each class are uniformly distributed;
NonIID means that the DP has a subset of classes, and the data
distributions vary for different DPs.
The Adversary. We consider two untargeted attacks, two tar-
geted attacks, and Sybil attack [32]. The untargeted attacks in-
clude sign-randomizing attack and free-rider attack [55]. The sign-
randomizing attack is an attack on the direction of the gradients
where the adversary randomly sets the sign as +1 or −1. In the
free-rider attack, we implement the delta weight attack [55], which
generates gradient updates by subtracting the two global models
received in the previous two epochs. The targeted attacks include
label-flipping attacks and backdoor attacks [9]. In a label-flipping at-
tack, the adversary swaps the labels of the two classes of data in the
training process to train poisoned local models. In the Sybil attack,
the adversary conjures up a number of clients and submit the same
compromised model. In the Sybil attack, we use the label-flipping
attack to train the malicious local models.
Evaluation Metrics. We use Main Task Accuracy (MTA) and
Attack Success Rate (ASR) as the evaluation metrics. MTA mea-
sures the classification accuracies of the trained models, while ASR
measures the fraction of poisoned samples that are predicted as
the target class in targeted attacks. Thus, higher MTAs indicate
more effective models, and lower ASRs indicate more robust models
against targeted attacks. We further define Data Acquisition Cost
(DAC) as the average percentage of local models that the DA must
procure in each training epoch in order to train the global model. In
general, the DA seeks to obtain high-performing models (i.e., with
high MTAs and low ASRs) at a reasonable DAC (lower the better).
Default Hyper-Parameters. For martFL, we set the threshold 𝑇
used in hierarchical clustering as 0.05 and the ratio of randomly
selected baseline candidates 𝛽 as 0.1. For Krum [12], we set the
proportion of possibly Byzantine as 20%. For CFFL [60], we set the
coefficient of reputation threshold as 1.0 and 𝛼 as 5. For RFFL [77],
we set the hyper-parameter 𝛼 as 0.95 and threshold as 1.0. For
RLR [65], we set the learning threshold 𝜃 is 0. For the backdoor
attack, we implement the attack proposed in [9] where the hyper-
parameter 𝛼 is 0.95.

6.2 Evaluation Results
Our evaluations are centered around the following questions:
• Accuracy. In § 6.2.1 and § 6.2.2, we quantitatively show that
martFL achieves the best MTAs compared to other server-driven
methods regardless of when the DA’s root dataset is biased or
not. Meanwhile, martFL reduces up to 69% DAC when achieving
comparable (if not better) MTAs with prior arts.

• Robustness. In § 6.2.3, we show that when facing with various
targeted attacks, untargeted attacks, and Sybil attack, martFL can
accurately identify malicious DPs and achieve the highest MTA
and lowest ASR in most cases, compared with prior arts.

• Accuracy Loss by Quantization. In § 6.2.4, we show that quan-
tization has little to no impact on the MTA of the global model.
• System Overhead. In § 6.2.5, we study the system overhead of
martFL, including the cryptography overhead during local model
evaluations, and the gas cost incurred for executing the trading
smart contract.

Dataset

TREC

AGNEWS

FMNIST

CIFAR

Biased Ratio

20%

30%

40%

Metric
CFFL
FLTrust
Ours
CFFL
FLTrust
Ours
CFFL
FLTrust
Ours
CFFL
FLTrust
Ours

MTA
76.87 ± 6.87
67.40 ± 4.76
88.80 ± 1.72
44.09 ± 1.95
44.09 ± 1.43
79.71 ± 2.15
88.37 ± 0.55
87.33 ± 0.48
88.22 ± 0.26
63.34 ± 0.22
10.00 ± 0.00
64.24 ± 0.06
Table 1: MTA (%) and DAC (%) when the DA possesses a biased root dataset.

MTA
81.53 ± 0.90
72.60 ± 1.07
87.53 ± 1.15
43.39 ± 1.57
45.19 ± 0.39
75.89 ± 1.30
88.48 ± 0.25
87.26 ± 0.38
88.88 ± 0.27
62.38 ± 0.33
11.42 ± 1.00
63.79 ± 0.28

MTA
79.07 ± 3.24
71.73 ± 1.09
87.20 ± 0.49
45.58 ± 1.46
43.65 ± 0.90
78.04 ± 1.08
88.02 ± 0.32
87.28± 0.39
87.71 ± 0.43
60.85 ± 0.80
14.25 ± 1.99
62.60 ± 0.37

DAC
100.00
36.52
53.63
100.00
11.52
36.30
100.00
32.64
35.14
100.00
7.59
53.63

DAC
100.00
46.47
53.88
100.00
10.35
38.99
100.00
33.57
30.06
100.00
10.79
53.88

DAC
100.00
40.15
51.79
100.00
11.89
41.15
100.00
46.04
39.60
100.00
1.30
51.79

6.2.1 Biased Root Dataset
First, we evaluate the MTA of prior art when the DA possesses an
unevenly distributed root dataset. Specifically, we consider that (i)
the root dataset of DA is dominated by half of the class labels; (ii) the
DPs follow the form of UNI in the number of samples; (iii) a certain
percentage of DPs have biased local dataset and the remaining DPs
have evenly distributed dataset (i.e., high-quality dataset with IID
distributions across all class labels).

We evaluate three different percentages of DPs (20%, 30%, and
40%) possessing biased local datasets. The results are reported in
Table 1. In terms of main task accuracy (MTA), martFL consistently
outperforms existing server-driven approaches, with particularly
significant advantages over FLTrust. We observed that all three
methods have very close MTAs on the FMNIST task. This may be
because the FMNIST task is relatively simple and we use a fairly
small model with approximately 44,000 parameters. The advan-
tages of martFL become more pronounced on larger models (for
instance, the models for both text classification tasks have ∼3 mil-
lion parameters, and the model for the CIFAR task has ∼1 million
parameters). The underlying reason for the MTA improvements
in martFL is because existing server-driven approaches has poor
inclusiveness when the root dataset is biased. To quantify this, we
plot the Cumulative Distribution Function (CDF) of inclusiveness
in Figure 5 for the TREC task with 20% biased DPs. We consider
both the inclusiveness of all the DPs and inclusiveness of only the
high-quality DPs. Because existing server-driven methods tend to
select local models with data distributions similar to the DA’s root
dataset, their selection of DPs is highly biased towards its root
dataset. On the contrary, benefited from the dynamic baseline ad-
justment design, martFL can include more high-quality DPs, even
if the root dataset is biased.

We further report DACs for all three methods, which represents
the average percentage of local models that the DA purchases in
each training epoch. The DAC in CFFL is always 100% because CFFL
must obtain all local models and evaluate their accuracies before
deciding whether or not to aggregate them. Therefore, the model
aggregation design in CFFL is undesirable in data marketplace,
where the DA prefers to only pay for high-quality local models from
the DPs. On the contrary, FLTrust has low DACs in this setting
because its local model selections are highly biased. As a result,
FLTrust has the lowest MTAs in nearly all tasks. martFL instead

10

(a)

(b)

Figure 5: The inclusiveness analysis when the DA posseses a
biased root dataset.

strikes a good balance between MTA and DAC, allowing the DA to
obtain high-performing global models with low cost.

6.2.2 Unbiased Root Dataset
In this segment, we evaluate the scenario where the DA’s root
dataset is unbiased. The total number of data samples owned by
each DP follows the POW distribution. However, each DP has
evenly distributed class labels. The results are shown in Table 2. In
general, when the root dataset is reliable, all three methods have
better MTAs than the case where the root dataset is biased. martFL
achieves slightly better or comparable MTAs compared with other
methods with the lowest DACs in all four tasks.

With the results in Table 1 and Table 2, we demonstrate that
(i) CFFL is slightly more resilient against a biased root dataset
than FLTrust. Yet, CFFL introduces consistently high DACs, which
is less desirable in data marketplace. (ii) FLTrust, on the other
hand, heavily depends on the root dataset, and can only achieve
comparable MTAs with CFFL when the root dataset is unbiased.
In contrast, martFL produces the best MTAs in nearly all cases
regardless of whether the root dataset is biased or not. Crucially,
martFL maintains the lowest DACs when achieving comparable
MTAs with the other two methods.

6.2.3 Robustness Against Various Attacks
In this case, we consider the robustness of martFL when facing
malicious DPs. We compare martFL with both client-driven and
server-driven approaches. Since we investigate nearly 700 differ-
ent combinations of approaches, attacks, and tasks, we train each
combination for a fixed number of 100 epochs in this segment.

0.00.20.40.60.81.0Inclusiveness0.00.51.0CDFOursFLTrustCFFL0.00.20.40.60.81.0High-quality Inclusiveness0.00.51.0CDFOursFLTrustCFFLDataset

Metric

CFFL
FLTrust
Ours

TREC

AGNEWS

FMNIST

CIFAR

MTA

DAC

MTA

DAC

MTA

DAC

MTA

85.47 ± 0.68
87.40 ± 0.71
87.67 ± 0.57

100.00
46.65
44.38

100.00
66.11
65.61
Table 2: MTA (%) and DAC (%) when the DA has an unbiased root dataset.

78.79 ± 1.03
80.94 ± 1.26
83.35 ± 1.54

89.22 ± 0.15
89.40 ± 0.20
89.88 ± 0.15

100.00
51.62
45.27

65.38 ± 0.50
70.66 ± 0.45
70.28 ± 0.27

DAC

100.00
39.44
34.89

(a) Free-rider Attack on TREC

(b) Sign-randomizing attack on TREC

(a) MTA(%) against Backdoor Attack
on TREC

(b) ASR(%) against Backdoor Attack on TREC

(c) Free-rider Attack on CIFAR

(d) Sign-randomizing attack on CIFAR

Figure 6: The MTA of the global model obtained by different
aggregation protocols under untargeted attacks.

(c) MTA(%) against Label-Flipping
Attack on TREC

(d) ASR(%) against Label-Flipping Attack on
TREC

First, Figure 6 presents the MTA of each scheme under free-rider
attack and sign-randomizing attack on the TREC and CIFAR dataset.
The result shows that martFL can defend against the attacks even
80% of the DPs are malicious. For the free-rider attack, the MTA
of martFL slightly decreases by 2.80% when the number of faulty
DPs increases from 30% to 80%. For the sign-randomizing attack,
the MTA of martFL remains consistent given different numbers of
faulty DPs.

Second, we plot the robustness of different aggregation schemes
against targeted attacks on the TREC and CIFAR dataset in Figure
7. The results show that martFL can achieve the highest MTA and
lowest ASR in most cases. When the proportion of attackers is
low, such as 30%, the MTA of RLR [65] is slightly better than other
approaches. However, as the proportion of attackers increases, the
performance all client-driven methods rapidly deteriorates. FLTrust
has comparable robustness with martFL. Note that in Figure 7(f),
the ASR of Krum and Median initially increase, but then decrease
to 0. However, the MTAs of both methods also decrease to zero, as
shown in Figure 7(e). This indicates that the global model is not
converged for both methods when the percentage of malicious DPs
is over 60%.

Finally, we evaluate the robustness of different schemes against
the Sybil attack in Figure 8. The experimental results show martFL
and FLTrust have comparable MTAs in most cases. In contrast,
the MTA of other methods decrease significantly as the number
of Sybil nodes increases. This is because the models submitted by
Sybil nodes are similar to each other, so that these schemes cannot
accurately eliminate the poisoned local model updates.

To sum up, CFFL and all the client-driven methods are more vul-
nerable to faulty DPs. For instance, some client-driven aggregation

(e) MTA(%) against Backdoor Attack
on CIFAR

(f) ASR(%) against Backdoor Attack on CIFAR

(g) MTA(%) against Label-Flipping
Attack on CIFAR
Figure 7: The MTA and ASR of the global model obtained by
different aggregation protocols under targeted attacks.

(h) ASR(%) against Label-Flipping Attack on
CIFAR

(a) TREC

(b) CIFAR

Figure 8: The MTA of the global model obtained by different
aggregation protocols against Sybil attacks.

protocols cannot produce a meaningful global model when the num-
ber of malicious DPs is sufficiently large. martFL achieves slightly
better robustness than FLTrust, yet reducing roughly 13.91% DAC
on average compared with FLTrust, as shown in Table 3.

11

304050607080Percentage of Malicious DPs20406080MTA(%)304050607080Percentage of Malicious DPs406080MTA(%)CFFLFLTrustFedAvgKrumMedianRFFLRLROurs304050607080Percentage of Malicious DPs204060MTA(%)304050607080Percentage of Malicious DPs204060MTA(%)CFFLFLTrustFedAvgKrumMedianRFFLRLROurs304050607080Percentage of Malicious DPs6080MTA(%)304050607080Percentage of Malicious DPs255075ASR(%)CFFLFLTrustFedAvgKrumMedianRFFLRLROurs304050607080Percentage of Malicious DPs406080MTA(%)304050607080Percentage of Malicious DPs255075ASR(%)CFFLFLTrustFedAvgKrumMedianRFFLRLROurs304050607080Percentage of Malicious DPs204060MTA(%)304050607080Percentage of Malicious DPs0204060ASR(%)CFFLFLTrustFedAvgKrumMedianRFFLRLROurs304050607080Percentage of Malicious DPs556065MTA(%)304050607080Percentage of Malicious DPs204060ASR(%)CFFLFLTrustFedAvgKrumMedianRFFLRLROurs304050607080Percentage of Malicious DPs607080MTA(%)304050607080Percentage of Malicious DPs5060MTA(%)CFFLFLTrustFedAvgKrumMedianRFFLRLROursTREC

Dataset ATK Ratio

LF

SY

FR

SR

BD

40%
84.38
68.32
53.32
57.28
68.60
51.50
81.24
52.40
62.64
48.10
63.24
60.36
53.82
63.38
72.20
60.64
74.38
60.73
65.38
50.53
In this table, “ATK” represents the type of attacks, “FR” represents the free-rider attack, “SR”
represents the sign-randomizing attack, “BD” represents the backdoor attack, “LF” represents the
label-flipping attack, and “SY” represents the Sybil attack.

FLTrust
Ours
FLTrust
Ours
FLTrust
Ours
FLTrust
Ours
FLTrust
Ours
FLTrust
Ours
FLTrust
Ours
FLTrust
Ours
FLTrust
Ours
FLTrust
Ours

80%
13.68
12.02
16.06
17.4
45.62
41.86
53.46
24.96
77.92
60.66
20.71
22.13
25.93
30.73
73.64
49.27
66.42
44.58
61.33
39.33

70%
24.22
20.26
26.18
22.82
53.72
29.64
58.04
30.02
70.40
50.62
35.82
33.02
33.33
46.16
69.67
70.62
70.42
54.42
64.38
40.36

60%
31.4
31.54
33.50
37.98
67.38
39.60
70.66
56.44
66.98
44.9
48.56
46.49
40.29
52.18
73.09
54.56
72.76
56.27
70.2
44.2

50%
36.18
46.58
42.82
40.3
72.24
46.60
65.28
47.92
66.72
48.68
51.09
45.91
47.24
57.6
68.27
54.44
72.69
63.07
68.42
48.27

30%
99.22
87.32
61.64
67.44
80.54
53.76
83.06
61.12
67.20
53.76
72.22
68.02
59.76
76.73
72.29
60.93
74.62
65.98
61.29
56.00

BD

SR

FR

SY

LF

CIFAR

*

Table 3: The DAC (%) comparison between FLTrust and
martFL in the robustness experiments. Results for CFFL are
omitted since they are always 100.

Dataset

MTA

𝑄𝑇

TREC
AGNEWS
FMNIST
CIFAR

88.60 ± 0.28
82.61 ± 0.65
90.07 ± 0.07
69.74 ± 0.62

F1

𝑄𝑇

87.58 ± 0.37
82.54 ± 0.65
90.03 ± 0.07
69.74 ± 0.69

Δ𝑄𝑇

-0.65 ↑
0.64 ↓
-0.20 ↑
0.53 ↓

Δ𝑄𝑇

-0.93 ↑
0.74 ↓
-0.20 ↑
0.54 ↓

Table 4: The MTA(%) and F1(%) loss in quantization.

6.2.4 Accuracy Loss by Quantization
To enable verifiable data transaction in martFL, the DA needs to first
quantize the prior model𝑊 𝑡 −1
and local model updates 𝑈 𝑡 , perform
𝑔
the aggregation to obtain quantized global model W𝑡
𝑔, and then de-
quantize the model to obtain a floating-point model 𝑊 𝑡
𝑔 . In this
segment, we study the impact of quantization on model accuracy.
We evaluate the scenario where the DA’s root dataset is unbiased.
The total number of data samples and the type of labels owned
by each DP follows the POW and IID distribution, respectively.
The results are summarized in Table 4. The Δ𝑄𝑇 represents the
MTA and F1 loss due to quantization (i.e., the accuracy difference
between 𝑊 𝑡
𝑔). The results indicate that the quantization
operations introduce negligible accuracy losses.

𝑔 and W𝑡

System-level Overhead

6.2.5
In this segment, we study the system-level overhead of (i), including
the cryptography overhead in our quality-aware model evaluation
protocol; (ii) the time and gas cost of the functions in the trading
smart contract.

In Figure 9, we plot the overhead of homomorphic encryption
and decryption operations. In the experiment, we set 4096 slots per
batch for the CKKS algorithm. The encryption time of the DA is
longer because the DA needs to perform homomorphic encryption,

Figure 9: The homo-
morphic ENC/DEC
times.

Figure 10: The gas cost and execution
times for verifying different numbers
of parameters.

Phrase

Prepare

Verify

Function

NE

Gas (wei)
Time (ms)

163076
92.0

Deposit
46643
73.6

CM

127731
80.6

Prepare CR(DA) CR(DP)
42632
38036
222018
70.6
64.2
83.4

Any

RE

-
74.8

*

In this table, “NE” represents NewEpoch, “CM” represents CommitModel,“CR” represents Claim-
Reward, and “RE” represents ReadEpoch.

Table 5: The gas costs and execution times of the functions
in our trading smart contract.

while DPs only needs to complete homomorphic additions. Overall,
the extra latency introduced by homomorphic operations is small.
Further, we report the gas cost and latency for executing different
functions in our trading smart contract in § 5.2. In the PreparePhase,
NewEpoch initializes training epochs, Deposit enables the DA to
deposit rewards and penalties. CommitModel allows the DPs to
commit local models, and Prepare records rewards and selects veri-
fication parameters. In the VerifyPhase, CommitInputs (abbreviated
as “CI”) allows the DA to commit the public inputs for aggregation
verification, and VerifyAggregation (abbreviated as “VA”) verifies
the integrity of aggregation. Both the DA and DPs can use ClaimRe-
ward to claim rewards. Finally, ReadEpoch is a convenience handle
to return detailed epoch information. The detailed implementations
are deferred to § A.1. Overall, none of these functions consumes
more than 0.25 × 106 wei, which costs less than 0.0025 US dollars
at the time of this writing.

In comparison to Omnilytics [54], which directly aggregates
local models via a smart contract, the gas cost in martFL is at least
1000 times smaller, when enabling approximately 8 times more
participants to collectively train models with ∼100 times more
parameters than the model trained in Omnilytics. In fact, the gas
cost in martFL is independent of the model size and the model
evaluation method, since martFL only verifies a fixed number (a
system setting) of randomly sampled model parameters. In addition,
the execution time of each function is less than 0.1 seconds. In
Figure 10, we also report the gas cost and execution time when
martFL has different system settings that verify different numbers
of model parameters.

6.3 Deep Dive
In this section, we further investigate several key design choices of
martFL and outline some future work.

12

102030# Parameters ×1030204060Time Cost (ms)DA_EncDP_EncDA_Dec100200300# of Verified Parameters24Time cost (s)CIVA100200300# of Verified Parameters0204060Gas Fee (Mwei)CIVA6.3.1 Tradeoff Between Inclusiveness and Robustness
In § 4.1, we presented the key observation that existing approaches
exhibit a fundamental tradeoff between inclusiveness and robust-
ness when aggregating local models submitted by the DPs. In this
segment, we further analyze this tradeoff by tuning the key pa-
rameters in the aggregation algorithms of both FLTrust [19] and
Krum [12]. Specifically, since FLTrust selects local models based on
the cosine similarities between them and the self-computed model
update trained on the DA’s root dataset, the key system parameter
that dictates the aggregation in FLTrust is the quality of the root
dataset. We quantify the quality as unbiasness ratio, which repre-
sents the percentage of class labels that predominates the DA’s root
dataset. For instance, FLTrust-1/3 represents the case where the root
dataset contains 1/3 of the class labels. In Krum, the key tunable
parameter 𝑓 is the proportion of Byzantine DPs defined in Krum’s
problem formulation, which directly determines the number of
local models selected for aggregation in each epoch. We evaluate
four Krum settings in this part (for instance, Krum-50 represents
the setting where the Krum algorithm is supposed to tolerate 50%
Byzantine DPs). We consider a group of heterogeneous DPs, where
30% of them hold high-quality data (evenly distributed across all
types of labels), 30% of them hold biased datasets in which the class
labels are dominated by half of the randomly selected labels, and
40% of them are malicious. We experiment both the targeted and
untargeted attacks on the TREC and FMNIST tasks.

The results are plotted in Figure 11, where inclusiveness is the
percentage of benign DPs whose local models are selected for aggre-
gation, and robustness is quantified as the percentage of malicious
DPs that are excluded for aggregation. We collect these two metrics
in each training epoch and report the average values. In general,
FLTrust can achieve reasonably good inclusiveness only if the unbi-
asness ratio of the DA’s root dataset is sufficiently large (e.g., reach-
ing 2/3). Krum, which is significantly impacted by its Byzantine
tolerance threshold 𝑓 , tends to have high robustness at the expense
of inclusiveness. In contrast, martFL achieves the best tradeoff be-
tween inclusiveness and robustness in all cases. As a result, martFL
consistently achieves high model performance regardless of the
parameter settings, as summarized in Table 6. However, FLTrust
begins to match martFL only when the root dataset is sufficiently
good. Krum fails to perform consistently across all tasks, regardless
of the parameter choices. For instance, although Krum-50 achieves
a good MTA on TREC against the sign-randomizing attack, it has a
significantly high ASR against the backdoor attack.

6.3.2 Analysis of Dynamic Baseline Adjustment
Quantitative Results. We first quantitatively analyze the accu-
racy of our Dynamic Baseline Adjustment algorithm (detailed in
§ 4.2.2) to demonstrate its robustness in various data distributions.
We consider a challenging setting for the DPs, where 30% of them
hold high-quality data and 30% of them hold biased datasets. The
remaining 40% of DPs have three different settings: (i) holding
evenly-distributed data, (ii) maliciously engaging the backdoor at-
tack (one type of targeted attack), and (iii) maliciously engaging the
sign-randomizing attack (one type of untargeted attack). In terms
of the root dataset held by the DA, we consider the following three
scenarios:

(a) Backdoor Attack on TREC

(b) Sign-Randomizing Attack on TREC

(c) Label-Flipping Attack on FMNIST

(d) Free-Rider Attack on FMNIST

Figure 11: Deep dive into the tradeoff between inclusiveness
and robustness in various settings.

LF

FR

BD

TREC

FMNIST

Dataset
Attack
Metric
FLTrust-1/3
FLTrust-1/2
FLTrust-2/3
Krum-10
Krum-30
Krum-50
Krum-70
Ours-1/3
Ours-1/2
Ours-2/3
* In this table, “BD” represents the backdoor attack, “SR” represents the
sign-randomizing attack, “LF” represents the label-flipping attack, and
“FR” represents the free-rider attack.

SR
ASR MTA MTA
74.71
57.08
12.02
87.11
57.48
9.01
87.97
83.16
9.43
88.43
32.08
41.58
86.44
38.24
68.27
80.64
67.04
85.08
85.97
78.64
36.23
87.94
76.36
7.75
75.36
88.23
8.43
79.96
88.65
7.55

ASR MTA
70.71
18.96
86.52
4.69
86.38
5.37
87.69
5.12
86.60
13.43
79.16
32.20
8.19
4.95
81.19
84.34
88.18

MTA
48.60
58.84
81.53
70.11
61.64
64.16
72.64
83.71
83.38
84.08

3.41
4.48
3.94

Table 6: The MTA (%) and ASR (%) in the inclusiveness-
robustness tradeoff experiments.

Attack

Scenarios

TREC AGNEWS

FMNIST CIFAR

None

BD

SR

Type-I Biased
Type-II Biased
Unbiased

Type-I Biased
Type-II Biased
Unbiased

Type-I Biased
Type-II Biased
Unbiased

80.00
88.33
95.67

65.33
80.33
79.00

64.67
96.00
95.67

70.00
98.67
100.00

58.00
93.00
84.00

62.67
88.67
99.67

98.80
100.00
99.67

96.67
95.33
100.00

98.67
99.00
99.33

95.67
100.00
99.67

95.00
100.00
100.00

99.00
89.50
91.50

“BD” and “SR” represent the backdoor and sign-randomizing attack, re-
spectively.

Table 7: The probability of selecting correct baselines by our
dynamic baseline adjustment algorithm.

• Type-I Bias. The root dataset of DA is dominated by half of
the class labels. The data distributions of the biased DPs are
similar to the distributions of the DA’s root dataset.

13

0.00.20.40.60.8Inclusiveness0.000.250.500.751.00RobustnessFLTrust-1/3FLTrust-1/2FLTrust-2/3Ours-1/3Ours-1/2Ours-2/3Krum-10Krum-30Krum-50Krum-700.20.40.60.81.0Inclusiveness0.20.40.60.81.0RobustnessFLTrust-1/3FLTrust-1/2FLTrust-2/3Ours-1/3Ours-1/2Ours-2/3Krum-10Krum-30Krum-50Krum-700.00.20.40.60.81.0Inclusiveness0.00.20.40.60.81.0RobustnessFLTrust-1/3FLTrust-1/2FLTrust-2/3Ours-1/3Ours-1/2Ours-2/3Krum-10Krum-30Krum-50Krum-700.000.250.500.75Inclusiveness0.000.250.500.751.00RobustnessFLTrust-1/3FLTrust-1/2FLTrust-2/3Ours-1/3Ours-1/2Ours-2/3Krum-10Krum-30Krum-50Krum-70• Type-II Bias. The root dataset of DA is dominated by half of
the class labels. However, the data distributions of the biased
DPs are random and independent of the DA’s data distributions.

• Unbiased. The root dataset of DA is evenly distributed.

leverage this insight by taking into account DPs’ historical reputa-
tions and incorporate momentum into our local model evaluation
algorithm. This approach may further reduce the reliance on the
DA’s root dataset and compensate for errors in baseline selections,
particularly in the Type-I Bias scenario.

We analyze the probability of selecting correct baselines during
the training process. A baseline is correct if the cosine similarity
between the baseline and the ground truth model update is strictly
positive. The ground truth update is an ideal update obtained di-
rectly on high-quality data, which can be considered as a hypothet-
ical scenario where the DA possesses sufficient high-quality data
and can train the model all by itself.

The results are reported in Table 7. The Type-I Bias is arguably
the most challenging scenario because the DA and the biased DPs
are similarly biased. Therefore, the DA is prone to be misled by
these biased DPs, resulting in possible incorrect baseline selections
(note that all our experiments in § 6.2 considered the Type-I Bias).
Nonetheless, our method still achieves reasonably accurate baseline
selections, up to 99% accuracy in the training epochs of the CIFRA
task. We also observed that the selection accuracies are affected by
the total number of class labels. Specifically, TREC and AGNEWS
have 6 and 4 class labels, respectively, while FMNIST and CIFAR
both have 10 class labels. When the total number of class labels is
smaller, the data diversity experienced by the biased DA and DPs
is even lower (for instance, they only see 2 labels in the AGNEWS
task). This results in a relatively higher probability of selecting
incorrect baselines. In the Type-II Bias scenario, where the data
distributions of the biased DPs and the DA are not correlated, the
probabilities of selecting correct baselines are higher than the Type-
I Bias scenario, even for the TREC and AGNEWS task. In fact,
the selection accuracies in the Type-II Bias scenario are already
comparable to the case where the DA’s root dataset is unbiased,
achieving over 90.0% baseline selection accuracy in most settings.
Attack Discussion. To attack our model aggregation protocol, an
adversary must carefully design local models that can be selected as
the baseline. However, there are several challenges to crafting such
models. First, the adversary has no access to the plaintext models
submitted by other DPs throughout the model aggregation and
transaction. Instead, the adversary only observes the commitments
of these local models, which do not reveal the actual local models.
Additionally, because the DA evaluates all local models offline using
the private model evaluation algorithm with its private root dataset,
it is difficult for the adversary to predict what types of local models
will receive higher Kappa scores in the root dataset. Finally, the
DA discloses the DP that is selected as the baseline for the current
epoch only after all DPs have committed their models. At this point,
the adversary cannot modify its committed local model, even if it
colludes with the selected DP. In summary, because martFL enables
complete offline and private local model evaluation and aggregation
by the DA, attacking our aggregation protocol is significantly more
difficult than existing blockchain-based approaches (e.g., Omnilyt-
ics [54], FPPDL [61]) that require the aggregation protocol to be
publicly observable on the blockchain.

Future Work on Model Evaluation and Aggregation Protocols.
Several studies [8, 43] have shown that historical information can
be used to identify Byzantine DPs. In our future work, we intend to

14

7 RELATED WORK
Robust FL Constructions. In § 6, we compared martFL with
five client-driven methods (FedAvg [62], RFFL [77], Krum [12], Me-
dian [78], and RLR [20]), and two server-driven methods (FLTrust [19]
and CFFL [60]). We summarize their core constructions here. McMa-
han et al. [62] proposed FedAvg, in which the aggregation weights
of the DPs are proportional to their data volumes. Yin et al. [78]
showed that aggregating local models using the median model up-
date or trimmed mean of all model updates can be effective against
Byzantine attacks. Yin et al. [12] proposed the Krum algorithm to
only choose the 𝑛−𝑓 −2 closest vectors from 𝑛 submitted vectors
(assuming the algorithm can tolerate up to 𝑓 Byzantine clients
among all 𝑛 clients) to aggregate the global model. Xu et al. [77]
designed the RFFL method, which first aggregates the global model,
then calculates the similarity between each local model and the
global model, and uses it as the reputation of the participants for
the aggregation in the next epoch. Ozdayi et al. [65] proposed RLR,
a lightweight aggregation method. To defend against the backdoor
attack, RLR carefully adjusts the aggregation server’s learning rate,
per dimension and per round, based on the sign information of
the updates from clients. Lyu et al. [60] proposed CFFL, a method
that validates the accuracy of data provider models based on local
datasets of the DA, and quantifies the collaborative fairness of each
DP via the correlation coefficient between participant contributions
and participant rewards. In aggregation, CFFL redefines the weight
of each model based on its test accuracy and historical contributions.
In FLTrust [20], the DA is required to hold a clean local dataset
to evaluate clients’ local updates. It compares the similarity in di-
rection and size between the server updates and the local model
updates for each participant and uses the similarity as the weight
to aggregate the global model.
Verifiable Protocols for Real-world Systems. The enthusiasm
for Web 3.0 [58] drives a growing number of literature on empower-
ing or even transforming real-world systems via verifiable (or trust-
free) protocols, such as verifiable cloud computing (e.g., [25, 51]),
decentralized digital good exchanges (e.g., [26]), smart contract
based legal sector transformation [29], and various proposals to im-
prove the Blockchain systems themselves (such as interoperability
(e.g., [57, 76, 80]) and private smart contracting [21, 44]). Overall,
practicability and deployability are two the primary challenges for
designing verifiable protocols to power real-world systems. Thus,
the verifiable transaction protocol in martFL only proves the crit-
ical computations that are necessary and sufficient to ensure fair
billing. It does not verify the entire FL training process, which
would otherwise impose unacceptably high overhead.
Data Pricing. Prior works have studied data pricing. For instance,
proposals [36, 42] evaluate data value based on Shapley Value using
a game theoretic approach, which typically requires access to the

full datasets. Some other literature (e.g., [10, 46]) propose a pric-
ing framework for relational queries. Data pricing for FL-based
marketplace is part of our future work.

8 CONCLUSION
In this paper, we propose martFL, a novel FL architecture that is
specifically designed to enable a utility-driven data marketplace.
Benefiting from the quality-aware model evaluation protocol, martFL
can eliminate the tradeoff between inclusiveness and robustness
when selecting desired DPs. Further, martFL designs a verifiable
transaction protocol that enables the DA to prove that it faithfully
aggregates model using the committed aggregations weights, en-
abling fair trading between the DA and DPs. We implemented a
prototype of martFL and extensively evaluated it on four datasets.
The experimental results demonstrate the accuracy, robustness and
efficiency of martFL.

REFERENCES
[1] Microsoft SEAL (release 3.7.2). https://github.com/Microsoft/SEAL, 2019.
[2] Libsnark: C++ Library for zkSNARKs. https://github.com/scipr-lab/libsnark,

2020.

[3] Bdex. https://www.bdex.com/, 2022.
[4] Arkworks-rs/Groth16: A Rust Implementation of the Groth16 zkSNARK. https:

//github.com/arkworks-rs/groth16, 2023.

[5] GE Predix Platform: Industrial IoT Platform: GE Digital. https://www.ge.com/

digital/iiot-platform, 2023.

[6] International Data Spaces Association. https://internationaldataspaces.org/, 2023.
[7] Quandl. https://demo.quandl.com/, 2023.
[8] Allen-Zhu, Z., Ebrahimian, F., Li, J., and Alistarh, D. Byzantine-resilient
Non-convex Stochastic Gradient Descent. In International Conference on Learning
Representations (ICLR) (2021).

[9] Bagdasaryan, E., Veit, A., Hua, Y., Estrin, D., and Shmatikov, V. How to
Backdoor Federated Learning. In International Conference on Artificial Intelligence
and Statistics (AISTATS) (2020).

[10] Balazinska, M., Howe, B., and Suciu, D. Data Markets in the Cloud: An

Opportunity for the Database Community. VLDB Endowment (2011).

[11] Bernstein, J., Zhao, J., Azizzadenesheli, K., and Anandkumar, A. signSGD
with Majority Vote is Communication Efficient and Fault Tolerant. In International
Conference on Learning Representations (ICLR) (2019).

[12] Blanchard, P., El Mhamdi, E. M., Guerraoui, R., and Stainer, J. Machine
Learning with Adversaries: Byzantine Tolerant Gradient Descent. In Advances in
Neural Information Processing Systems (NeurIPS) (2017).

[13] Bonawitz, K., Ivanov, V., Kreuter, B., Marcedone, A., McMahan, H. B., Patel,
S., Ramage, D., Segal, A., and Seth, K. Practical Secure Aggregation for Privacy-
preserving Machine Learning. In Proceedings of the ACM SIGSAC Conference on
Computer and Communications Security (CCS) (2017).

[14] Boneh, D., Bonneau, J., Bünz, B., and Fisch, B. Verifiable Delay Functions. In

Annual International Cryptology Conference (CRYPTO) (2018).

[15] Bowe, S., Grigg, J., and Hopwood, D. Recursive Proof Composition without a

Trusted Setup. In Cryptology ePrint Archive (2019).

[16] Bünz, B., Bootle, J., Boneh, D., Poelstra, A., Wuille, P., and Maxwell, G.
In IEEE

Bulletproofs: Short Proofs for Confidential Transactions and More.
Symposium on Security and Privacy (SP) (2018).

[17] Bünz, B., Fisch, B., and Szepieniec, A. Transparent SNARKs from DARK Com-
In Annual International Conference on the Theory and Applications of

pilers.
Cryptographic Techniques (EUROCRYPT) (2020).

[18] Calzada, I. Citizens’s Data Privacy in China: The State of the Art of the Personal

Information Protection Law (PIPL). In Smart Cities (2022).

[19] Cao, X., Fang, M., Liu, J., and Gong, N. Z. FLTrust: Byzantine-robust Federated
In ISOC Network and Distributed System

Learning via Trust Bootstrapping.
Security Symposium (NDSS) (2021).

[20] Chen, H., Asif, S. A., Park, J., Shen, C.-C., and Bennis, M. Robust Blockchained
Federated Learning with Model Validation and Proof-of-Stake Inspired Consensus.
In AAAI Conference on Artificial Intelligence (AAAI) Workshop on Towards Robust,
Secure and Efficient Machine Learning (2021).

[21] Cheng, R., Zhang, F., Kos, J., He, W., Hynes, N., Johnson, N., Juels, A., Miller,
A., and Song, D. Ekiden: A Platform for Confidentiality-preserving, Trustworthy,
and Performant Smart Contracts. In IEEE European Symposium on Security and
Privacy (2019).

[22] Cheon, J. H., Kim, A., Kim, M., and Song, Y. Homomorphic Encryption for
Arithmetic of Approximate Numbers. In Advances in Cryptology–ASIACRYPT

2017: 23rd International Conference on the Theory and Applications of Cryptology
and Information Security (ASIACRYPT) (2017).

[23] Chiesa, A., Hu, Y., Maller, M., Mishra, P., Vesely, N., and Ward, N. Marlin:
Preprocessing zkSNARKs with Universal and Updatable SRS. In Advances in
Cryptology–EUROCRYPT 2020: 39th Annual International Conference on the Theory
and Applications of Cryptographic Techniques (EUROCRYPT) (2020).

[24] Cohen, J. A coefficient of agreement for nominal scales. In Educational and

psychological measurement (1960).

[25] Dong, C., Wang, Y., Aldweesh, A., McCorry, P., and van Moorsel, A. Betrayal,
Distrust, and Rationality: Smart Counter-Collusion Contracts for Verifiable Cloud
Computing. In Proceedings of the 2017 ACM SIGSAC conference on computer and
communications security (CCS) (2017).

[26] Dziembowski, S., Eckey, L., and Faust, S. Fairswap: How to Fairly Exchange
Digital Goods. In Proceedings of the 2018 ACM SIGSAC conference on computer
and communications security (CCS) (2018).

[27] Eberhardt, J., and Tai, S. ZoKrates - Scalable Privacy-Preserving Off-Chain
Computations. In IEEE International Conference on Internet of Things (iThings) and
IEEE Green Computing and Communications (GreenCom) and IEEE Cyber, Physical
and Social Computing (CPSCom) and IEEE Smart Data (SmartData) (2018).
[28] Fang, M., Cao, X., Jia, J., and Gong, N. Local Model Poisoning Attacks to
In 29th USENIX Security Symposium

Byzantine-Robust Federated Learning.
(USENIX Security 20) (2020).

[29] Fang, P., Zou, Z., Xiao, X., and Liu, Z. iSyn: Semi-automated Smart Contract
Synthesis from Legal Financial Agreements. In Proceedings of the 32nd ACM
SIGSOFT International Symposium on Software Testing and Analysis (ISSTA) (2023).
[30] Feng, B., Qin, L., Zhang, Z., Ding, Y., and Chu, S. ZEN: An Optimizing Compiler
for Verifiable, Zero-knowledge Neural Network Inferences. In Cryptology ePrint
Archive (2021).

[31] Fernandez, R. C., Subramaniam, P., and Franklin, M. J. Data Market Plat-
forms: Trading Data Assets to Solve Data Problems. In Proceedings of the VLDB
Endowment (2020).

[32] Fung, C., Yoon, C. J., and Beschastnikh, I. The Limitations of Federated
Learning in Sybil Settings. In International Symposium on Research in Attacks,
Intrusions and Defenses (RAID) (2020).

[33] Gabizon, A., Williamson, Z. J., and Ciobotaru, O. PLONK:Permutations over
Lagrange-bases for Oecumenical Noninteractive arguments of Knowledge. In
Cryptology ePrint Archive (2019).

[34] Garay, J., Kiayias, A., and Leonardos, N. The Bitcoin Backbone Protocol with
Chains of Variable Difficulty. In Advances in Cryptology–CRYPTO 2017: 37th
Annual International Cryptology Conference (CRYPTO) (2017).

[35] Ghodsi, Z., Gu, T., and Garg, S. SafetyNets: Verifiable Execution of Deep
Neural Networks on an Untrusted Cloud. In Proceedings of the 31st International
Conference on Neural Information Processing Systems (NeurIPS) (2017).

[36] Ghorbani, A., and Zou, J. Data Shapley: Equitable Valuation of Data for Machine

Learning. In International conference on machine learning (2019).

[37] Goldwasser, S., Micali, S., and Rackoff, C. The Knowledge Complexity of
Interactive Proof-systems. In SIAM Journal on Computing (SICOMP) (1989).
[38] Gorbunov, S. Algorand Releases First Open-Source Code of Verifiable
Random Function. https://algorand.com/resources/algorand-announcements/
algorand-releases-first-open-source-code-of-verifiable-random-function, 2018.
[39] Grassi, L., Khovratovich, D., Rechberger, C., Roy, A., and Schofnegger, M.
Poseidon: A New Hash Function for Zero-Knowledge Proof Systems. In 30th
USENIX Security Symposium (USENIX Security 21) (2021).

[40] Groth, J. On the Size of Pairing-based Non-interactive Arguments. In Advances
in Cryptology–EUROCRYPT 2016: 35th Annual International Conference on the
Theory and Applications of Cryptographic Techniques (EUROCRYPT) (2016).
[41] Jacob, B., Kligys, S., Chen, B., Zhu, M., Tang, M., Howard, A., Adam, H., and
Kalenichenko, D. Quantization and training of neural networks for efficient
integer-arithmetic-only inference.
In Proceedings of the IEEE Conference on
Computer Vision and Pattern Recognition (CVPR) (2018).

[42] Jia, R., Dao, D., Wang, B., Hubis, F. A., Hynes, N., Gürel, N. M., Li, B., Zhang,
C., Song, D., and Spanos, C. J. Towards Efficient Data Valuation Based on the
Shapley Value. In The 22nd International Conference on Artificial Intelligence and
Statistics (2019).

[43] Karimireddy, S. P., He, L., and Jaggi, M. Learning from History for Byzantine

Robust Optimization. In International Conference on Machine Learning (2021).

[44] Kosba, A., Miller, A., Shi, E., Wen, Z., and Papamanthou, C. Hawk: The
Blockchain Model of Cryptography and Privacy-preserving Smart Contracts. In
IEEE Symposium on Security and Privacy (SP) (2016).

[45] Kothapalli, A., Setty, S., and Tzialla, I. Nova: Recursive Zero-Knowledge
Arguments from Folding Schemes. In Advances in Cryptology – CRYPTO 2022:
42nd Annual International Cryptology Conference (CRYPTO) (2022).

[46] Koutris, P., Upadhyaya, P., Balazinska, M., Howe, B., and Suciu, D. Toward

Practical Query Pricing with Querymarket. In ACM SIGMOD (2013).

[47] Koutsos, V., Papadopoulos, D., Chatzopoulos, D., Tarkoma, S., and Hui, P.
Agora: A Privacy-aware Data Marketplace. In IEEE 40th International Conference
on Distributed Computing Systems (ICDCS) (2020).

15

[48] Krishnamachari, B., Power, J., Kim, S. H., and Shahabi, C. I3: An IoT Market-
place for Smart Communities. In Proceedings of the 16th Annual International
Conference on Mobile Systems, Applications, and Services (MobiSys) (2018).
[49] Krizhevsky, A. Learning Multiple Layers of Features from Tiny Images. Tech.

rep., University of Toronto, 2009.

[50] LeCun, Y., Boser, B., Denker, J. S., Henderson, D., Howard, R. E., Hubbard, W.,
and Jackel, L. D. Backpropagation Applied to Handwritten Zip Code Recognition.
In Neural computation (1989).

[51] Li, P., Wang, Y., Liu, Z., Xu, K., Wang, Q., Shen, C., and Li, Q. Verifying the
Quality of Outsourced Training on Clouds. In European Symposium on Research
in Computer Security (2022).

[52] Li, Q., Liu, Z., Li, Q., and Xu, K. martFL: Enabling Utility-Driven Data Marketplace
with a Robust and Verifiable Federated Learning Architecture. In Proceedings
of the 2023 ACM SIGSAC Conference on Computer and Communications Security
(CCS) (2023), pp. 1496–1510.

[53] Li, X., and Roth, D. Learning Question Classifiers. In 19th International Confer-

ence on Computational Linguistics (COLING) (2002).

[54] Liang, J., Li, S., Jiang, W., Cao, B., and He, C. OmniLytics: A Blockchain-
based Secure Data Market for Decentralized Machine Learning. In International
Workshop on Federated Learning for User Privacy and Data Confidentiality in
Conjunction with ICML (FL-ICML) (2021).

[55] Lin, J., Du, M., and Liu, J. Free-riders in Federated Learning: Attacks and

Defenses. In arXiv preprint arXiv:1911.12560 (2019).

[56] Liu, T., Xie, X., and Zhang, Y. ZkCNN: Zero Knowledge Proofs for Convolutional
Neural Network Predictions and Accuracy. In Proceedings of the 2021 ACM SIGSAC
Conference on Computer and Communications Security (CCS) (2021).

[57] Liu, Z., Xiang, Y., Shi, J., Gao, P., Wang, H., Xiao, X., Wen, B., and Hu, Y.-
C. Hyperservice: Interoperability and Programmability across Heterogeneous
Blockchains. In Proceedings of the 2019 ACM SIGSAC conference on computer and
communications security (CCS) (2019).

[58] Liu, Z., Xiang, Y., Shi, J., Gao, P., Wang, H., Xiao, X., Wen, B., Li, Q., and Hu,
Y.-C. Make Web3. 0 Connected. IEEE Transactions on Dependable and Secure
Computing (TDSC) (2022).

[59] Lloyd, S. Least Squares Quantization in PCM. In IEEE Transactions on Information

Theory (1982).

[60] Lyu, L., Xu, X., Wang, Q., and Yu, H. Collaborative Fairness in Federated

Learning. In Federated Learning: Privacy and Incentive (2020).

[61] Lyu, L., Yu, J., Nandakumar, K., Li, Y., Ma, X., Jin, J., Yu, H., and Ng, K. S. Towards
Fair and Privacy-preserving Federated Deep Models. In IEEE Transactions on
Parallel and Distributed Systems (TPDS) (2020).

[62] McMahan, B., Moore, E., Ramage, D., Hampson, S., and y Arcas, B. A.
Communication-efficient Learning of Deep Networks From Decentralized Data.
In Proceedings of the 20th International Conference on Artificial Intelligence and
Statistics (AISTATS) (2017).

[63] Micali, S., Rabin, M., and Vadhan, S. Verifiable Random Functions. In Annual

Symposium on Foundations of Computer Science (FOCS) (1999).

[64] Niu, C., Zheng, Z., Wu, F., Gao, X., and Chen, G. Achieving Data Truthfulness
and Privacy Preservation in Data Markets. In IEEE Transactions on Knowledge
and Data Engineering (TKDE) (2018).

[65] Ozdayi, M. S., Kantarcioglu, M., and Gel, Y. R. Defending Against Backdoors
in Federated Learning with Robust Learning Rate. In Proceedings of the AAAI
Conference on Artificial Intelligence (AAAI) (2021).

[66] Paszke, A., Gross, S., Massa, F., Lerer, A., Bradbury, J., Chanan, G., Killeen, T.,

Lin, Z., Gimelshein, N., Antiga, L., Desmaison, A., Köpf, A., Yang, E., DeVito,
Z., Raison, M., Tejani, A., Chilamkurthy, S., Steiner, B., Fang, L., Bai, J., and
Chintala, S. PyTorch: An Imperative Style, High-Performance Deep Learning
Library. In Proceedings of the 33rd International Conference on Neural Information
Processing Systems (NeurIPS) (2019).

[67] Rabin, M. O. Transaction Protection by Beacons. In Journal of Computer and

System Sciences (JCSS) (1983).

[68] Rousseeuw, P. J. Silhouettes: A Graphical Aid to the Interpretation and Validation
of Cluster Analysis. In Journal of Computational and Applied Mathematics (1987).

[69] Thorndike, R. Who Belongs in the Family? In Psychometrika (1953).
[70] Tibshirani, R., Walther, G., and Hastie, T. Estimating the Number of Clusters
in a Data Set Via the Gap Statistic. In Journal of the Royal Statistical Society Series
B: Statistical Methodology (2001).

[71] Vitalik, B., and Christian, R. Eip-197: Precompiled contracts for optimal ate
pairing check on the elliptic curve alt_bn128. https://eips.ethereum.org/EIPS/
eip-197, 2017. Ethereum Improvement Proposals, no. 197.

[72] Voigt, P., and Von dem Bussche, A. The EU General Data Protection Regulation

(GDPR): A Practical Guide.

[73] Wahby, R. S., Tzialla, I., Shelat, A., Thaler, J., and Walfish, M. Doubly-
Efficient zkSNARKs Without Trusted Setup. In 2018 IEEE Symposium on Security
and Privacy (SP) (2018).

[74] Wood, G. Ethereum: A Secure Decentralised Generalised Transaction Ledger. In

Ethereum Project Yellow Paper (2014).

[75] Xiao, H., Rasul, K., and Vollgraf, R. Fashion-MNIST: A Novel Image Dataset for
Benchmarking Machine Learning Algorithms. In arXiv preprint arXiv:1708.07747
(2017).

[76] Xie, T., Zhang, J., Cheng, Z., Zhang, F., Zhang, Y., Jia, Y., Boneh, D., and Song,
D. zkBridge: Trustless Cross-chain Bridges Made Practical. In Proceedings of the
ACM SIGSAC Conference on Computer and Communications Security (CCS) (2022).
[77] Xu, X., and Lyu, L. A Reputation Mechanism Is All You Need: Collaborative
Fairness and Adversarial Robustness in Federated Learning. In International
Workshop on Federated Learning for User Privacy and Data Confidentiality in
Conjunction with ICML (FL-ICML) (2021).

[78] Yin, D., Chen, Y., Kannan, R., and Bartlett, P. Byzantine-robust Distributed
Learning: Towards Optimal Statistical Rates. In Proceedings of the 35th Interna-
tional Conference on Machine Learning (ICML) (2018).

[79] Yoon, K. Convolutional Neural Networks for Sentence Classification. In Confer-
ence on Empirical Methods in Natural Language Processing (EMNLP) (2014).
[80] Zamyatin, A., Harz, D., Lind, J., Panayiotou, P., Gervais, A., and Knottenbelt,
W. Xclaim: Trustless, Interoperable, Cryptocurrency-Backed Assets. In IEEE
Symposium on Security and Privacy (SP) (2019).

[81] Zhang, J., Fang, Z., Zhang, Y., and Song, D. Zero Knowledge Proofs for Decision
Tree Predictions and Accuracy. In Proceedings of the ACM SIGSAC Conference on
Computer and Communications Security (CCS) (2020).

[82] Zhang, X., Zhao, J., and LeCun, Y. Character-Level Convolutional Networks for
Text Classification. In Proceedings of the 28th International Conference on Neural
Information Processing Systems (NeurIPS) (2015).

[83] Zhao, L., Wang, Q., Wang, C., Li, Q., Shen, C., and Feng, B. Veriml: Enabling
integrity assurances and fair payments for machine learning as a service. In IEEE
Transactions on Parallel and Distributed Systems (TPDS) (2021).

[84] Zhu, Z., Shu, J., Zou, X., and Jia, X. Advanced Free-rider Attacks in Federated
Learning. In the 1st NeurIPS Workshop on New Frontiers in Federated Learning
Privacy, Fairness, Robustness, Personalization and Data Ownership (2021).

16

A APPENDIX
A.1 Pseudocode of Trading Smart Contract
The pseudocode of our trading smart contract is presented in Algo-
rithm 3.
Data Structure. In smart contracts, we have designed three data
structures: Verification Key (VK), Proof (PF), and Training Epochs
(EP). To achieve verifiable computation in smart contracts, we utilize
the ALT_BN128 elliptic curve, which is supported in Ethereum EIP-
197 [71].

The EP data structure stores information related to a training
epoch. It includes fields such as 𝑑𝑒𝑝𝑜𝑠𝑖𝑡𝐷𝑃 and 𝑑𝑒𝑝𝑜𝑠𝑖𝑡𝐷𝐴, repre-
senting the deposit funds for the DP and DA, respectively. Other
fields include 𝑑𝑒𝑙𝑎𝑦, which specifies the delay time for registration,
and 𝑡𝑠, representing the timestamp of when the epoch was initiated.
The 𝑠𝑎𝑚𝑝𝑙𝑒𝑠 field is an array of verified parameters, and the 𝑎𝑚𝑜𝑢𝑛𝑡
and 𝑚𝑜𝑑𝑒𝑙 fields are mappings representing the tokens for each DP
and model information from them, respectively. The boolean fields
𝑖𝑠𝑅𝑒𝑔𝑖𝑠𝑡𝑒𝑟 , 𝑖𝑠𝑃𝑟𝑒𝑝𝑎𝑟𝑒𝑑, and 𝑖𝑠𝑉 𝑒𝑟𝑖 𝑓 𝑖𝑒𝑑 indicate whether the regis-
ter phase, preparation phase, and verification phase are completed,
respectively. The 𝑖𝑠𝐹𝑎𝑖𝑙𝑒𝑑 identifier presents the verification result.
Training setup. At the beginning of the model training, the smart
contract initializes the training process Training as an empty set. As
the training proceeds, the Training set stores the training data for
each epoch in an EP data structure. The Training set can be used for
source tracing and supervision of transactions. The smart contract
also records the number of completed training epochs 𝑛𝑢𝑚𝐸𝑝𝑜𝑐ℎ
and the number of registered DPs 𝑛𝑢𝑚𝐷𝑃. The DA is responsible
for deploying the contract, so dataAcquirer is set to the address of
the message sender in the constructor. We use the onlyDA keyword
to restrict certain functions to be called only by the DA.
Epoch Initialization. The DA can use the function NewEpoch
to initiate a new epoch. It creates a new EP structure, adds it to
Training, and deposits the initial funds through msg.value. If it is
not the first epoch, the smart contract will check whether the pre-
vious epoch has been verified. This is to ensure that the DA cannot
start a new epoch until the fund allocation for the previous epoch
is complete. In addition, when a new epoch is created successfully,
the model registration time for the epoch is set to delay, and the
smart contract broadcasts an event to notify all DPs. Also, DPs can
use ReadEpoch to get the EP to check the details of the epoch.
Deposit Reward and Penalty. The DA can use the NewEpoch
and Deposit functions to deposit the funds in the epoch, including
the rewards for DPs proportional to their weights in K𝑡 and the
penalty if it cannot later provide a correct proof. When the epoch
is initialized, the DA can use msg.value to deposit the initial funds.
Additionally, during the registration phase, the DA can continue
to increase funding to attract more DPs to participate in training.
After each increase in funding, the smart contract also emits an
EpochDeposit event to notify all DPs. Therefore, when DA invokes
the Deposit function, the smart contract checks whether it is still
in the registration phase. Once the registration phase is completed,
the DA will no longer be able to deposit funds.
Commit Models. The CommitModel function allows DPs to com-
mit their local models on the smart contract, which means they
participate in the current epoch of training. Upon receiving no-
tification of the NewEpoch event, DPs can commit local models

17

agg

by CommitModel function. During the model registration phase,
DPs need to upload the hash and signature of their model in or-
der to prevent malicious DPs from uploading incorrect models in
subsequent model transactions, which ensures the integrity and se-
curity of data transactions. When the registration phase has ended,
the CommitModel function sets the Training[epoch].𝑖𝑠𝑅𝑒𝑔 flag to
true, indicating that the round of model registration has ended
and unregistered DPs cannot participate in the current epoch of
training.
Prepare Aggregation. In the preparation phase, the DA needs
to accomplish two goals through the smart contract. The first is
to record the amount of reward allocated to each participant. The
allocation ratio needs to be consistent with the aggregation weight
of each local model in order to ensure the success of verification.
The second goal is to select the verification parameters for random
sampling through the VDF function. The VDF function based on
smart contracts ensures that the generation of random numbers is
not influenced by the DA or DPs. After achieving these goals, the
smart contract sets Training[epoch].isPrepared to true.
Commit Public Inputs. After the preparation phase, the DA
should commit the verification key 𝑣𝑘, the quantized public in-
put X𝑡,𝑐 , and the proof 𝜋𝑡
to blockchain by the CommitInputs
function for later verification.
Verify Aggregation.Verifying the integrity of aggregation is the
main goal of the smart contract. Because the VerifyAggregation
function requires a large amount of gas fee, the smart contract
ensures that each epoch only needs to be validated successfully
once. The core function of the verification phase is Verify, which
implements the zk-SNARKs verification of the aggregation process.
To reduce the gas fees required for contract deployment, we use
the verifying key parameter as the input value for contract exe-
cution. We have designed the ConstructVK function, which can
reconstruct the verifying key by the input parameters. Otherwise,
in order to perform zk-SNARKs verification within the block gas
limit, we use the precompiled contracts in Ethereum for elliptic
curve pairing operations, such as the Addition, ScalarMul, Negate,
etc. After the successful execution of VerifyAggregation, the smart
contract broadcasts EpochVerified event to all the DPs to claim
their reward.
Claim Reward. After the aggregation is verified, DPs can with-
draw their reward from the smart contract account through the
ClaimReward function at any time. The smart contract will check
whether the address of the DP belongs to a participant in the train-
ing round to prevent malicious DPs from claiming funds. Also, after
the smart contract sends the relevant return to the DPs, the par-
ticipant’s proportion in the smart contract is set to 0. This means
that if a DP attempts to withdraw his reward again, they will only
consume the gas fee and receive no additional reward.

A.2 The CNN Architecture of the Global Model

Used for CIFAR Dataset

Table 8 shows the architecture of the Convolutional Neural Network
(CNN) used for the CIFAR [49] dataset in our experiments.

The CNN has seven layers. The first layer is the input layer, which
takes in images that are 32 × 32 pixels with three channels. The next

Algorithm 3: The Pseudocode of Our Trading Smart Contract

1 struct G1Point { uint X, uint Y }
2 struct G2Point {uint [2] X, uint [2] Y }
3 struct PF { G1Point 𝑎, 𝑐, G2Point 𝑏 }# Proof
4 struct VK { G1Point 𝛼, G1Point [] 𝛾𝑎𝑏𝑐 , G2Point 𝛽, 𝛾 , 𝛿 }
5 struct EP { # Epoch
6

uint depositDP, # The deposit fund for DPs.
uint depositDA,# The deposit fund for DA.
uint delay, # The maximum delay for registering in this epoch.
uint ts, # The timestamp to initiate this epoch.
uint [] samples, # The verified parameters.

7

8

9

bool isRegister, isPrepared, isVerified, isFailed }

10
11 mapping (uint => uint) amount,# The reward amount for each DP.
12 mapping (uint => string) model,# The model info from each DP.
13 mapping (string =>uint []) inputs, # The public inputs in verification.
14
15 init Training := ∅, dataAcquirer := null
16 init numEP := 0, numDP := 0. # Number of epoch and DP.
17 init mapping (address => uint) dataProvider := ∅
18 constructor (): dataAcquirer := msg.sender
19 modifier onlyDA : require msg.sender = dataAcquirer
20 Function CommitModel(epoch, hash, sig)public :
21

require epoch< numEP and Training [epoch].isRegister = false
EP e := Training [epoch]
if ((block.timestamp- e.ts) > e.delay) :

22

23

24

25

26

27

31

32

33

34

35

36

e.isRegister := true, abort# register phase is done

else :

if msg.sender not in dataAcquirer.keys :

dataProvider[msg.sender] := numDP, numDP ++

e.model[dataProvider[msg.sender]] := hash, sig

28
29 Function NewEpoch(𝑇 ) public payable onlyDA :
30

if numEP!=0 :# Only when the previous epoch is verified

require (Training [numEP-1].isVerified = true)

EP e := Training.push()
e.depositDA, e.depositDP := msg.value/2
e.isRegister,e.isPrepared,e.isVerified,e.isFailed := false
e.ts := block.timestamp, e.delay := 𝑇
emit NewEpoch(numEP,e.ts + e.delay)
numEP ++

37
38 Function ReadEpoch(epoch)public :
39

require epoch< numEP
return Training [epoch]

40
41 Function Prepare(epoch, addr, amount, num) public onlyDA :
42

require epoch< numEP and Training [epoch].isPrepared = false
require Len(addr) = Len(amount)
EP e := Training [epoch]
if ((block.timestamp- e.ts) > e.delay) : e.isRegister := true
else : abort# register phase in progress
require Len(addr) = Len(e.model) and sum(num) = sum(e.amount)
for 𝑖 in range(Len(addr)) : e.amount[addr[i]] := amount[i]
e.samples,𝜋vdf := VDF(num) # generate ramdom samples
e.isPrepared := true

43

44

45

46

47

48

49

50

, X𝑡,𝑐 )public onlyDA :

emit EpochPrepareEvent(epoch)
51
52 Function CommitInputs(epoch, 𝑣𝑘, 𝜋𝑡
53

agg

54

55

require epoch< numEP and Training [epoch].isPrepared = true
require Training [epoch].isVerified = false
Training [epoch].inputs = 𝑣𝑘, 𝜋𝑡
agg
emit CommitPublicInputs(epoch)
56
57 Function VerifyAggregation(epoch) public :
58

, X𝑡,𝑐

require epoch< numEP
EP e := Training [epoch]
require e.isPrepared = true and e.isVerified = false
𝑣𝑘, X𝑡,𝑐, 𝜋𝑡
agg = e.inputs
𝑣 := Verify(𝑣𝑘, X𝑡,𝑐, 𝜋𝑡
e.isVerified := true
if 𝑣 = false :

agg )

e.isFailed := true
for id in e.amount.keys :

e.amount[id] += e.depositDA/Len(e.amount.keys)

e.depositDA := 0

emit EpochVerified(epoch,𝑣)
69
70 Function Verify(𝑣𝑘, 𝑥, 𝜋 ) private :
71

require Len(𝑥) +1 = Len(vk.𝛾𝑎𝑏𝑐 )
G1Point tmp := G1Point (0, 0)
for 𝑖 in range(Len(𝑥)) :

require (x[i] < p) # 𝑝 the large prime number in G2 group.
tmp := Addition(tmp, ScalarMul(vk.𝛾𝑎𝑏𝑐 [i + 1], x[i]))

tmp := Addition(tmp, vk.𝑔𝑎𝑚𝑚𝑎𝑎𝑏𝑐 [0])
𝑝1 := 𝜋 .a, Negate(tmp),Negate(𝜋 .c),Negate(vk.𝛼)
𝑝2 := 𝜋 .b, vk.𝛾 ,vk.𝛿,vk.𝛽
return PairingCheck(𝑝1,𝑝2)

79
80 Function Deposit(epoch) public payable onlyDA :
81

require epoch< numEP and Training [epoch].isRegister = false
EP e := Training [epoch]
if ((block.timestamp- e.ts) > e.delay) :

e.isRegister := true, abort# register phase is done

else :

e.depositDA += msg.value/2;
e.depositDP += msg.value/2;
emit EpochDepositEvent(epoch,e.depositDA)

88
89 Function ClaimReward(epoch) public payable :
90

require epoch< numEP and Training [epoch].isVerified = true
EP e := Training [epoch]
ID := dataProvider[msg.sender]
require msg.sender = dataAcquirer or ID in e.model.keys
if msg.sender = dataAcquirer :
amount := e.depositDA
if (msg.sender.send(amount)) : e.depositDA := 0

else :

amount := e.amount[ID]
if (msg.sender.send(amount)) : e.amount[ID] := 0

59

60

61

62

63

64

65

66

67

68

72

73

74

75

76

77

78

82

83

84

85

86

87

91

92

93

94

95

96

97

98

99

18

Layer

Size

Input
CNN + ReLU, Max Pooling
CNN + ReLU, Max Pooling
CNN + ReLU, Max Pooling
Linear + ReLU
Linear + ReLU
Linear + ReLU
Linear

3 × 32 × 32
3 × 15 × 3, 2 × 2
15 × 75 × 4, 2 × 2
75 × 375 × 3, 2 × 2
1500 × 400
400 × 120
120 × 84
84 × 10

Table 8: The CNN architecture of the global model used for
CIFAR [49] dataset.

three layers are CNN layers, each followed by a ReLU activation
function and a max pooling layer. The first CNN layer has 15 filters
of size 3 × 3, the second CNN layer has 75 filters of size 4 × 4, and the
third CNN layer has 375 filters of size 3 × 3. The size of kernels in all
max pooling layers is 2 × 2. The last four layers are fully connected
layers, each followed by a ReLU activation function, except for the
last one. The size of fully connected layers are 1500 × 400, 400 × 120,
120 × 84, and 84 × 10, respectively. The output of the last layer
is a vector of 10 numbers, which represent the probability of the
input image belonging to each of the 10 classes. The class with the
highest probability is the predicted class of the input image.

19

