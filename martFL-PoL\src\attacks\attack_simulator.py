"""
Attack Simulator for POL-martFL Integration
基于用户经验实现各种联邦学习攻击类型
"""

import torch
import torch.nn as nn
import numpy as np
import copy
from typing import Dict, List, Optional, Any


class AttackSimulator:
    """
    攻击模拟器
    实现拜占庭攻击、伪造攻击、模型替换攻击等
    """
    
    def __init__(self, attack_config: Dict[str, Any]):
        """
        初始化攻击模拟器
        
        Args:
            attack_config: 攻击配置字典
        """
        self.attack_config = attack_config
        self.attack_history = []
        
        # 拜占庭攻击配置
        self.byzantine_attack = attack_config.get('byzantine_attack', False)
        self.attack_type = attack_config.get('attack_type', 'random')
        self.attack_prob = attack_config.get('attack_prob', 0.5)
        
        # 伪造/重放攻击配置
        self.fake_update = attack_config.get('fake_update', False)
        self.fake_update_type = attack_config.get('fake_update_type', 'zero')
        self.fake_update_probability = attack_config.get('fake_update_probability', 0.3)
        
        # 模型替换攻击配置
        self.model_tampering = attack_config.get('model_tampering', False)
        self.tampering_layer = attack_config.get('tampering_layer', 'all')
        self.tampering_ratio = attack_config.get('tampering_ratio', 0.1)
        
        # 存储原始模型用于重放攻击
        self.stored_models = []
    
    def apply_byzantine_attack(self, model: nn.Module, gradients: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        应用拜占庭攻击
        
        Args:
            model: 当前模型
            gradients: 原始梯度
            
        Returns:
            攻击后的梯度
        """
        if not self.byzantine_attack:
            return gradients
        
        # 根据概率决定是否执行攻击
        random_val = np.random.random()
        if random_val > self.attack_prob:
            print(f"      [DEBUG] Attack skipped: random={random_val:.3f} > prob={self.attack_prob}")
            return gradients

        print(f"      [DEBUG] Attack triggered: random={random_val:.3f} <= prob={self.attack_prob}")
        
        attacked_gradients = {}
        
        for name, grad in gradients.items():
            if self.attack_type == 'random':
                # 随机梯度攻击
                attacked_gradients[name] = torch.randn_like(grad) * grad.std()
                
            elif self.attack_type == 'invert':
                # 反转梯度攻击
                attacked_gradients[name] = -grad
                
            elif self.attack_type == 'const':
                # 常量梯度攻击
                attacked_gradients[name] = torch.ones_like(grad) * 0.01
                
            elif self.attack_type == 'zero':
                # 零梯度攻击
                attacked_gradients[name] = torch.zeros_like(grad)
                
            else:
                attacked_gradients[name] = grad
        
        self.attack_history.append({
            'type': 'byzantine',
            'attack_type': self.attack_type,
            'step': len(self.attack_history)
        })
        
        return attacked_gradients
    
    def apply_fake_update_attack(self, model: nn.Module, original_weights: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        应用伪造/重放攻击
        
        Args:
            model: 当前模型
            original_weights: 原始权重
            
        Returns:
            攻击后的权重
        """
        if not self.fake_update:
            return {name: param.data.clone() for name, param in model.named_parameters()}
        
        # 根据概率决定是否执行攻击
        if np.random.random() > self.fake_update_probability:
            return {name: param.data.clone() for name, param in model.named_parameters()}
        
        attacked_weights = {}
        
        for name, param in model.named_parameters():
            if self.fake_update_type == 'zero':
                # 零更新攻击（"白嫖"）
                attacked_weights[name] = original_weights[name].clone()
                
            elif self.fake_update_type == 'random':
                # 随机更新攻击
                attacked_weights[name] = torch.randn_like(param.data) * param.data.std()
                
            elif self.fake_update_type == 'replay':
                # 重放攻击：使用之前存储的权重
                if self.stored_models:
                    replay_model = np.random.choice(self.stored_models)
                    if name in replay_model:
                        attacked_weights[name] = replay_model[name].clone()
                    else:
                        attacked_weights[name] = param.data.clone()
                else:
                    attacked_weights[name] = param.data.clone()
            else:
                attacked_weights[name] = param.data.clone()
        
        self.attack_history.append({
            'type': 'fake_update',
            'fake_update_type': self.fake_update_type,
            'step': len(self.attack_history)
        })
        
        return attacked_weights
    
    def apply_model_tampering_attack(self, model: nn.Module) -> nn.Module:
        """
        应用模型替换/篡改攻击
        
        Args:
            model: 原始模型
            
        Returns:
            篡改后的模型
        """
        if not self.model_tampering:
            return model
        
        tampered_model = copy.deepcopy(model)
        
        # 确定要篡改的层
        layer_names = [name for name, _ in tampered_model.named_parameters()]
        
        if self.tampering_layer == 'all':
            target_layers = layer_names
        elif self.tampering_layer == 'first':
            target_layers = [layer_names[0]] if layer_names else []
        elif self.tampering_layer == 'last':
            target_layers = [layer_names[-1]] if layer_names else []
        elif isinstance(self.tampering_layer, list):
            target_layers = [name for name in layer_names if name in self.tampering_layer]
        else:
            target_layers = []
        
        # 篡改选定的层
        for name, param in tampered_model.named_parameters():
            if name in target_layers:
                # 随机篡改一定比例的参数
                mask = torch.rand_like(param.data) < self.tampering_ratio
                noise = torch.randn_like(param.data) * param.data.std() * 0.5
                param.data[mask] += noise[mask]
        
        self.attack_history.append({
            'type': 'model_tampering',
            'tampering_layer': self.tampering_layer,
            'tampering_ratio': self.tampering_ratio,
            'step': len(self.attack_history)
        })
        
        return tampered_model
    
    def store_model_for_replay(self, model: nn.Module, max_stored: int = 5):
        """
        存储模型状态用于重放攻击
        
        Args:
            model: 要存储的模型
            max_stored: 最大存储数量
        """
        model_state = {name: param.data.clone() for name, param in model.named_parameters()}
        self.stored_models.append(model_state)
        
        # 限制存储数量
        if len(self.stored_models) > max_stored:
            self.stored_models.pop(0)
    
    def simulate_poisoned_data_training(self, dataloader: torch.utils.data.DataLoader, 
                                      poison_ratio: float = 0.1) -> torch.utils.data.DataLoader:
        """
        模拟有毒数据训练
        
        Args:
            dataloader: 原始数据加载器
            poison_ratio: 投毒比例
            
        Returns:
            包含有毒数据的数据加载器
        """
        dataset = dataloader.dataset
        poisoned_data = []
        poisoned_targets = []
        
        for i, (data, target) in enumerate(dataset):
            if np.random.random() < poison_ratio:
                # 标签翻转攻击
                poisoned_target = (target + 1) % 10  # 假设10个类别
                poisoned_data.append(data)
                poisoned_targets.append(poisoned_target)
            else:
                poisoned_data.append(data)
                poisoned_targets.append(target)
        
        poisoned_dataset = torch.utils.data.TensorDataset(
            torch.stack(poisoned_data),
            torch.tensor(poisoned_targets)
        )
        
        return torch.utils.data.DataLoader(
            poisoned_dataset,
            batch_size=dataloader.batch_size,
            shuffle=True
        )
    
    def get_attack_statistics(self) -> Dict[str, Any]:
        """获取攻击统计信息"""
        stats = {
            'total_attacks': len(self.attack_history),
            'attack_types': {},
            'attack_config': self.attack_config
        }
        
        for attack in self.attack_history:
            attack_type = attack['type']
            if attack_type not in stats['attack_types']:
                stats['attack_types'][attack_type] = 0
            stats['attack_types'][attack_type] += 1
        
        return stats
    
    def is_malicious_client(self) -> bool:
        """判断是否为恶意客户端"""
        return (self.byzantine_attack or 
                self.fake_update or 
                self.model_tampering)


class AttackConfigGenerator:
    """攻击配置生成器"""
    
    @staticmethod
    def generate_honest_config() -> Dict[str, Any]:
        """生成诚实客户端配置"""
        return {
            'byzantine_attack': False,
            'fake_update': False,
            'model_tampering': False
        }
    
    @staticmethod
    def generate_byzantine_config(attack_type: str = 'random', 
                                attack_prob: float = 0.5) -> Dict[str, Any]:
        """生成拜占庭攻击配置"""
        return {
            'byzantine_attack': True,
            'attack_type': attack_type,
            'attack_prob': attack_prob,
            'fake_update': False,
            'model_tampering': False
        }
    
    @staticmethod
    def generate_fake_update_config(fake_type: str = 'zero',
                                  fake_prob: float = 0.3) -> Dict[str, Any]:
        """生成伪造更新攻击配置"""
        return {
            'byzantine_attack': False,
            'fake_update': True,
            'fake_update_type': fake_type,
            'fake_update_probability': fake_prob,
            'model_tampering': False
        }
    
    @staticmethod
    def generate_tampering_config(tampering_layer: str = 'all',
                                tampering_ratio: float = 0.1) -> Dict[str, Any]:
        """生成模型篡改攻击配置"""
        return {
            'byzantine_attack': False,
            'fake_update': False,
            'model_tampering': True,
            'tampering_layer': tampering_layer,
            'tampering_ratio': tampering_ratio
        }
    
    @staticmethod
    def generate_mixed_attack_config() -> Dict[str, Any]:
        """生成混合攻击配置"""
        return {
            'byzantine_attack': True,
            'attack_type': 'random',
            'attack_prob': 0.3,
            'fake_update': True,
            'fake_update_type': 'zero',
            'fake_update_probability': 0.2,
            'model_tampering': False
        }
