"""
POL-martFL Integrator
将POL验证集成到martFL的质量评估协议中
实现分层验证策略：POL安全过滤 → martFL质量选择
"""

import torch
import torch.nn as nn
import numpy as np
import time
from typing import Dict, List, Tuple, Optional, Any

from byzantine_pol_verifier import ByzantinePOLVerifier


class POLMartFLIntegrator:
    """
    POL-martFL集成器
    在martFL Algorithm 1的第6行之前插入POL验证层
    """
    
    def __init__(self, 
                 device: torch.device,
                 pol_enabled: bool = True,
                 distance_metric: str = 'cosine',
                 fault_tolerance_ratio: float = 0.33):
        """
        初始化POL-martFL集成器
        
        Args:
            device: 计算设备
            pol_enabled: 是否启用POL验证
            distance_metric: POL距离度量
            fault_tolerance_ratio: 拜占庭容错比例
        """
        self.device = device
        self.pol_enabled = pol_enabled
        
        # POL验证器
        if self.pol_enabled:
            self.pol_verifier = ByzantinePOLVerifier(
                device=device,
                distance_metric=distance_metric,
                fault_tolerance_ratio=fault_tolerance_ratio
            )
        
        # 集成统计
        self.integration_stats = {
            'total_rounds': 0,
            'pol_filtered_clients': 0,
            'martfl_selected_clients': 0,
            'integration_times': []
        }
    
    def integrated_client_selection(self, 
                                  client_models: List[nn.Module],
                                  client_pol_proofs: List[Optional[Dict[str, Any]]],
                                  client_ids: List[int],
                                  test_dataloader: torch.utils.data.DataLoader,
                                  criterion: nn.Module) -> Tuple[List[int], Dict[str, Any]]:
        """
        集成的客户端选择策略
        分层验证：POL安全过滤 → martFL质量选择
        
        Args:
            client_models: 客户端模型列表
            client_pol_proofs: 客户端POL证明列表
            client_ids: 客户端ID列表
            test_dataloader: 测试数据加载器
            criterion: 损失函数
            
        Returns:
            (选中的客户端ID列表, 详细结果)
        """
        start_time = time.time()
        
        print(f"\n[POL-martFL Integrator] Starting integrated client selection...")
        print(f"  Total clients: {len(client_models)}")
        print(f"  POL enabled: {self.pol_enabled}")
        
        # 第一层：POL安全过滤
        if self.pol_enabled:
            pol_verification_result = self.pol_verifier.verify_all_clients(client_pol_proofs)

            pol_results = pol_verification_result['verification_results']
            pol_base_weights = pol_verification_result['pol_base_weights']

            # 过滤通过POL验证的客户端
            pol_verified_indices = [i for i, verified in enumerate(pol_results) if verified]
            pol_verified_models = [client_models[i] for i in pol_verified_indices]
            pol_verified_ids = [client_ids[i] for i in pol_verified_indices]
            pol_verified_weights = [pol_base_weights[i] for i in pol_verified_indices]

            print(f"  POL Layer: {len(pol_verified_indices)}/{len(client_models)} clients verified")

            if not pol_verified_indices:
                print(f"  Warning: No clients passed POL verification!")
                return [], {
                    'pol_verified_count': 0,
                    'martfl_selected_count': 0,
                    'pol_results': pol_results,
                    'pol_base_weights': pol_base_weights,
                    'martfl_results': None,
                    'integration_time': time.time() - start_time
                }
        else:
            # 如果未启用POL，所有客户端都通过第一层，权重均等
            pol_verified_indices = list(range(len(client_models)))
            pol_verified_models = client_models
            pol_verified_ids = client_ids
            pol_verified_weights = [1.0] * len(client_models)  # 均等权重
            pol_results = [True] * len(client_models)
            pol_base_weights = [1.0] * len(client_models)

            print(f"  POL Layer: Disabled, all {len(client_models)} clients pass through")
        
        # 第二层：martFL质量选择（传入POL基础权重）
        martfl_results = self._martfl_quality_assessment(
            pol_verified_models,
            pol_verified_ids,
            test_dataloader,
            criterion,
            pol_base_weights=pol_verified_weights  # 传入POL基础权重
        )
        
        # 选择高质量客户端
        selected_client_ids = self._select_high_quality_clients(
            pol_verified_ids, 
            martfl_results
        )
        
        print(f"  martFL Layer: {len(selected_client_ids)}/{len(pol_verified_models)} clients selected")
        print(f"  Final selection: {selected_client_ids}")
        
        integration_time = time.time() - start_time
        
        # 更新统计
        self.integration_stats['total_rounds'] += 1
        self.integration_stats['pol_filtered_clients'] += len(pol_verified_indices)
        self.integration_stats['martfl_selected_clients'] += len(selected_client_ids)
        self.integration_stats['integration_times'].append(integration_time)
        
        return selected_client_ids, {
            'pol_verified_count': len(pol_verified_indices),
            'martfl_selected_count': len(selected_client_ids),
            'pol_results': pol_results,
            'martfl_results': martfl_results,
            'integration_time': integration_time,
            'selected_client_ids': selected_client_ids
        }
    
    def _martfl_quality_assessment(self,
                                  models: List[nn.Module],
                                  client_ids: List[int],
                                  test_dataloader: torch.utils.data.DataLoader,
                                  criterion: nn.Module,
                                  pol_base_weights: List[float] = None) -> Dict[int, Dict[str, float]]:
        """
        martFL质量评估 - 渐进式结合POL权重

        Args:
            models: 通过POL验证的模型列表
            client_ids: 对应的客户端ID列表
            test_dataloader: 测试数据加载器
            criterion: 损失函数
            pol_base_weights: POL基础权重列表

        Returns:
            每个客户端的质量评估结果
        """
        print(f"    [martFL Assessment] Evaluating {len(models)} models with POL weights...")

        if pol_base_weights is None:
            pol_base_weights = [1.0] * len(models)

        quality_results = {}

        for i, (model, client_id, pol_weight) in enumerate(zip(models, client_ids, pol_base_weights)):
            # 评估模型性能
            accuracy, loss = self._evaluate_model(model, test_dataloader, criterion)

            # 原martFL质量评分
            martfl_score = self._compute_quality_score(accuracy, loss)

            # 渐进式结合：POL基础权重 × martFL评分
            combined_score = pol_weight * martfl_score

            quality_results[client_id] = {
                'accuracy': accuracy,
                'loss': loss,
                'martfl_score': martfl_score,
                'pol_weight': pol_weight,
                'combined_score': combined_score
            }

            print(f"      Client {client_id}: acc={accuracy:.4f}, loss={loss:.4f}, "
                  f"martFL={martfl_score:.4f}, POL={pol_weight:.3f}, combined={combined_score:.4f}")

        return quality_results
    
    def _select_high_quality_clients(self, 
                                   client_ids: List[int],
                                   quality_results: Dict[int, Dict[str, float]]) -> List[int]:
        """
        选择高质量客户端 - 基于martFL的分层聚类思想
        
        Args:
            client_ids: 客户端ID列表
            quality_results: 质量评估结果
            
        Returns:
            选中的高质量客户端ID列表
        """
        if not quality_results:
            return []
        
        # 提取组合分数（POL权重 × martFL分数）
        score_key = 'combined_score' if 'combined_score' in quality_results[client_ids[0]] else 'quality_score'
        scores = [quality_results[cid][score_key] for cid in client_ids]

        print(f"    [Client Selection] Using {score_key} for selection from {len(client_ids)} candidates")

        if len(scores) <= 2:
            # 如果客户端太少，全部选择
            return client_ids

        # 简化的分层选择策略
        # 基于martFL的思想：选择分数高于中位数的客户端
        median_score = np.median(scores)

        selected_ids = []
        for cid in client_ids:
            if quality_results[cid][score_key] >= median_score:
                selected_ids.append(cid)

        # 确保至少选择一个客户端
        if not selected_ids and client_ids:
            # 选择分数最高的客户端
            best_client = max(client_ids, key=lambda cid: quality_results[cid][score_key])
            selected_ids = [best_client]

        # 显示选择详情
        print(f"    Selected {len(selected_ids)} clients: {selected_ids}")
        for cid in selected_ids:
            result = quality_results[cid]
            if 'combined_score' in result:
                print(f"      Client {cid}: combined={result['combined_score']:.4f} "
                      f"(POL={result['pol_weight']:.3f} × martFL={result['martfl_score']:.4f})")
            else:
                print(f"      Client {cid}: score={result['quality_score']:.4f}")
        
        return selected_ids
    
    def _evaluate_model(self, 
                       model: nn.Module, 
                       dataloader: torch.utils.data.DataLoader,
                       criterion: nn.Module) -> Tuple[float, float]:
        """评估模型性能"""
        model.eval()
        total_loss = 0.0
        correct = 0
        total = 0
        
        with torch.no_grad():
            for data, target in dataloader:
                data, target = data.to(self.device), target.to(self.device)
                output = model(data)
                loss = criterion(output, target)
                
                total_loss += loss.item() * data.size(0)
                pred = output.argmax(dim=1, keepdim=True)
                correct += pred.eq(target.view_as(pred)).sum().item()
                total += data.size(0)
        
        accuracy = correct / total if total > 0 else 0.0
        avg_loss = total_loss / total if total > 0 else float('inf')
        
        return accuracy, avg_loss
    
    def _compute_quality_score(self, accuracy: float, loss: float) -> float:
        """
        计算质量分数 - 基于martFL的质量评估思想
        
        Args:
            accuracy: 准确率
            loss: 损失值
            
        Returns:
            质量分数 (越高越好)
        """
        # 简化的质量分数计算
        # 结合准确率和损失，准确率越高、损失越低，分数越高
        
        # 归一化损失 (避免除零)
        normalized_loss = 1.0 / (1.0 + loss)
        
        # 加权组合
        quality_score = 0.7 * accuracy + 0.3 * normalized_loss
        
        return quality_score
    
    def get_integration_stats(self) -> Dict[str, Any]:
        """获取集成统计信息"""
        stats = self.integration_stats.copy()
        
        if stats['total_rounds'] > 0:
            stats['avg_pol_filtered_per_round'] = stats['pol_filtered_clients'] / stats['total_rounds']
            stats['avg_martfl_selected_per_round'] = stats['martfl_selected_clients'] / stats['total_rounds']
        else:
            stats['avg_pol_filtered_per_round'] = 0.0
            stats['avg_martfl_selected_per_round'] = 0.0
        
        if stats['integration_times']:
            stats['avg_integration_time'] = np.mean(stats['integration_times'])
        else:
            stats['avg_integration_time'] = 0.0
        
        # 添加POL验证器统计
        if self.pol_enabled:
            pol_stats = self.pol_verifier.get_verification_stats()
            stats['pol_verifier_stats'] = pol_stats
        
        return stats
