#!/bin/bash

# 更新系统包
sudo apt-get update

# 安装Python和pip
sudo apt-get install -y python3 python3-pip python3-venv

# 创建虚拟环境
python3 -m venv /tmp/venv

# 激活虚拟环境并添加到profile
echo "source /tmp/venv/bin/activate" >> ~/.profile

# 激活虚拟环境
source /tmp/venv/bin/activate

# 升级pip
pip install --upgrade pip

# 进入工作目录
cd /mnt/persist/workspace

# 安装所有必要的依赖
pip install torch==1.13.0 torchvision==0.14.0 scipy==1.7.3 numpy==1.21.5 pytest

# 安装martFL的其他依赖
pip install gap_stat==2.0.1 ghapi==1.0.4 kmeans1d==0.3.1 nox==2023.4.22 pandas==1.3.5 rich==13.5.3 scikit_learn==1.0.2 setuptools==65.5.0 sphinx_rtd_theme==1.3.0 torcheval==0.0.6 torchtext==0.6.0

# 验证安装
python -c "import torch; print('PyTorch version:', torch.__version__)"
python -c "import numpy; print('NumPy version:', numpy.__version__)"
python -c "import scipy; print('SciPy version:', scipy.__version__)"

# 创建一个综合测试脚本
cat > comprehensive_test.py << 'EOF'
#!/usr/bin/env python3
"""
综合测试脚本 - 测试martFL和Proof-of-learning项目的核心功能
"""

import sys
import os
import torch
import numpy as np

def test_martfl_quantization():
    """测试martFL的量化功能"""
    print("=== 测试 martFL 量化功能 ===")
    
    # 添加martFL src目录到路径
    sys.path.insert(0, 'martFL/src')
    
    try:
        from quant_basic_opt import (
            floatpoint_minimum_maximum, 
            calculate_scaling_factor, 
            zero_point, 
            check_quantization_error
        )
        
        # 测试基本量化功能
        floatpoint_array = np.random.randn(1000)
        min_val, max_val = floatpoint_minimum_maximum(floatpoint_array)
        scaler = calculate_scaling_factor(min_val, max_val)
        zeropoint = zero_point(max_val, scaler)
        quant_error = check_quantization_error(floatpoint_array, scaler, zeropoint)
        
        print(f"✓ 基本量化测试通过")
        print(f"  - 数据范围: [{min_val:.4f}, {max_val:.4f}]")
        print(f"  - 量化误差范围: [{quant_error.min():.6f}, {quant_error.max():.6f}]")
        
        return True
    except Exception as e:
        print(f"✗ martFL量化测试失败: {e}")
        return False

def test_martfl_aggregation():
    """测试martFL的聚合量化功能"""
    print("\n=== 测试 martFL 聚合量化功能 ===")
    
    try:
        from quant_aggregation_opt import (
            check_quant_multiply_error,
            check_quant_add_error
        )
        
        # 测试量化乘法
        floatpoint_array_a = np.random.rand(1, 20)
        floatpoint_array_b = np.random.rand(20, 1000)
        check_quant_multiply_error(floatpoint_array_a, floatpoint_array_b)
        print("✓ 量化乘法测试通过")
        
        # 测试量化加法
        floatpoint_array_c = np.random.rand(1, 1000)
        floatpoint_array_d = np.random.rand(1, 1000)
        check_quant_add_error(floatpoint_array_c, floatpoint_array_d)
        print("✓ 量化加法测试通过")
        
        return True
    except Exception as e:
        print(f"✗ martFL聚合量化测试失败: {e}")
        return False

def test_proof_of_learning_models():
    """测试Proof-of-learning的模型功能"""
    print("\n=== 测试 Proof-of-learning 模型功能 ===")
    
    # 添加Proof-of-learning目录到路径
    sys.path.insert(0, 'Proof-of-learning/Proof-of-Learning-main')
    
    try:
        import model as custom_model
        
        # 测试不同的ResNet模型
        models_to_test = [
            ('resnet20', custom_model.resnet20),
            ('resnet32', custom_model.resnet32),
            ('resnet44', custom_model.resnet44),
        ]
        
        for model_name, model_func in models_to_test:
            net = model_func()
            
            # 计算参数数量
            total_params = sum(p.numel() for p in net.parameters() if p.requires_grad)
            total_layers = len([p for p in net.parameters() if p.requires_grad and len(p.shape) > 1])
            
            print(f"✓ {model_name} 模型创建成功")
            print(f"  - 参数数量: {total_params}")
            print(f"  - 层数: {total_layers}")
            
            # 测试前向传播
            with torch.no_grad():
                dummy_input = torch.randn(1, 3, 32, 32)
                output = net(dummy_input)
                print(f"  - 输出形状: {output.shape}")
        
        return True
    except Exception as e:
        print(f"✗ Proof-of-learning模型测试失败: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("\n=== 测试基本功能 ===")
    
    try:
        # 测试PyTorch基本功能
        x = torch.randn(10, 10)
        y = torch.randn(10, 10)
        z = torch.mm(x, y)
        print(f"✓ PyTorch矩阵乘法测试通过，结果形状: {z.shape}")
        
        # 测试NumPy基本功能
        a = np.random.randn(100)
        b = np.random.randn(100)
        c = np.dot(a, b)
        print(f"✓ NumPy点积测试通过，结果: {c:.4f}")
        
        # 测试SciPy基本功能
        from scipy import stats
        data = np.random.normal(0, 1, 1000)
        stat, p_value = stats.normaltest(data)
        print(f"✓ SciPy正态性检验测试通过，p值: {p_value:.4f}")
        
        return True
    except Exception as e:
        print(f"✗ 基本功能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始综合测试...")
    print("=" * 50)
    
    test_results = []
    
    # 运行所有测试
    test_results.append(test_basic_functionality())
    test_results.append(test_martfl_quantization())
    test_results.append(test_martfl_aggregation())
    test_results.append(test_proof_of_learning_models())
    
    # 总结结果
    print("\n" + "=" * 50)
    print("测试结果总结:")
    passed = sum(test_results)
    total = len(test_results)
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试都通过了！")
        return 0
    else:
        print("⚠️  部分测试失败")
        return 1

if __name__ == "__main__":
    exit(main())
EOF

# 运行综合测试
python comprehensive_test.py

echo "测试完成！"