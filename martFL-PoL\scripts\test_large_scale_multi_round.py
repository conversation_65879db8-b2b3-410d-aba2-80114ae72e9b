#!/usr/bin/env python3
"""
Large Scale Multi-Round POL Integration Test
大规模多轮次POL集成测试
≥10 communication rounds, ≥20 clients
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import time
import random
import json
from collections import defaultdict

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.join(current_dir, '..', 'src', 'integration'))
sys.path.append(os.path.join(current_dir, '..', 'src', 'attacks'))

from pol_martfl_trainer import POLMartFLTrainer
from pol_martfl_integrator import POLMartFLIntegrator
from attack_simulator import AttackConfigGenerator


def create_realistic_model():
    """创建更现实的模型"""
    return nn.Sequential(
        nn.Flatten(),
        nn.Linear(10, 64),
        nn.<PERSON>L<PERSON>(),
        nn.Dropout(0.3),
        nn.Linear(64, 32),
        nn.<PERSON>L<PERSON>(),
        nn.Dropout(0.2),
        nn.Linear(32, 4)  # 四分类
    )


def create_non_iid_datasets(n_clients=24, base_size=300):
    """创建非IID数据集，模拟真实联邦学习场景"""
    datasets = []
    
    for client_id in range(n_clients):
        torch.manual_seed(42 + client_id * 13)
        
        # 创建不同的数据分布（非IID）
        if client_id % 6 == 0:
            # 类型1：主要包含类别0和1
            X = torch.randn(base_size, 10) * 0.8
            y = torch.randint(0, 2, (base_size,))
        elif client_id % 6 == 1:
            # 类型2：主要包含类别1和2
            X = torch.randn(base_size, 10) * 0.9 + 0.5
            y = torch.randint(1, 3, (base_size,))
        elif client_id % 6 == 2:
            # 类型3：主要包含类别2和3
            X = torch.randn(base_size, 10) * 1.1 - 0.3
            y = torch.randint(2, 4, (base_size,))
        elif client_id % 6 == 3:
            # 类型4：主要包含类别0和3
            X = torch.randn(base_size, 10) * 0.7 + 0.2
            y = torch.randint(0, 4, (base_size,))
            y[y == 1] = 0
            y[y == 2] = 3
        elif client_id % 6 == 4:
            # 类型5：均匀分布但有偏移
            X = torch.randn(base_size, 10) * 1.2 + torch.randn(10) * 0.5
            y = torch.randint(0, 4, (base_size,))
        else:
            # 类型6：复杂分布
            X = torch.randn(base_size, 10)
            X[:, :5] *= 1.5  # 前5个特征放大
            y = ((X[:, 0] + X[:, 1] - X[:, 2]) > 0).long()
            y += ((X[:, 3] + X[:, 4]) > 0).long()
            y = torch.clamp(y, 0, 3)
        
        # 添加客户端特定的噪声
        noise_level = 0.05 + (client_id % 7) * 0.02
        X += torch.randn_like(X) * noise_level
        
        dataset = torch.utils.data.TensorDataset(X, y)
        datasets.append(dataset)
    
    return datasets


def federated_averaging(models, weights=None):
    """联邦平均聚合"""
    if weights is None:
        weights = [1.0 / len(models)] * len(models)
    
    # 归一化权重
    total_weight = sum(weights)
    weights = [w / total_weight for w in weights]
    
    # 创建聚合模型
    aggregated_model = create_realistic_model()
    aggregated_state = aggregated_model.state_dict()
    
    # 加权平均
    for key in aggregated_state:
        aggregated_state[key] = torch.zeros_like(aggregated_state[key])
        for i, model in enumerate(models):
            aggregated_state[key] += weights[i] * model.state_dict()[key]
    
    aggregated_model.load_state_dict(aggregated_state)
    return aggregated_model


def test_large_scale_multi_round():
    """大规模多轮次POL集成测试"""
    print("=" * 80)
    print("Large Scale Multi-Round POL Integration Test")
    print("≥10 Communication Rounds, ≥20 Clients")
    print("=" * 80)
    
    torch.manual_seed(42)
    np.random.seed(42)
    random.seed(42)
    device = torch.device('cpu')
    
    # 实验配置
    n_clients = 24
    n_rounds = 12  # 12轮通信
    malicious_ratio = 0.25
    local_epochs = 2
    learning_rate = 0.02
    participation_rate = 0.8  # 每轮80%的客户端参与
    
    print(f"Experiment Configuration:")
    print(f"  Total clients: {n_clients}")
    print(f"  Communication rounds: {n_rounds}")
    print(f"  Malicious ratio: {malicious_ratio:.0%}")
    print(f"  Participation rate: {participation_rate:.0%}")
    print(f"  Local epochs: {local_epochs}")
    print(f"  Learning rate: {learning_rate}")
    
    # 创建数据集
    client_datasets = create_non_iid_datasets(n_clients, base_size=200)
    test_dataset = create_non_iid_datasets(1, base_size=150)[0]
    test_dataloader = torch.utils.data.DataLoader(test_dataset, batch_size=75, shuffle=False)
    criterion = nn.CrossEntropyLoss()
    
    # 定义恶意客户端
    n_malicious = int(n_clients * malicious_ratio)
    malicious_indices = random.sample(range(n_clients), n_malicious)
    malicious_indices.sort()
    
    print(f"  Malicious clients: {malicious_indices} ({len(malicious_indices)} total)")
    
    # 多样化攻击配置
    attack_configs = {}
    attack_types = ['invert', 'random', 'scale']
    attack_probs = [0.7, 0.8, 0.9, 1.0]
    
    for i, mal_idx in enumerate(malicious_indices):
        attack_type = attack_types[i % len(attack_types)]
        attack_prob = attack_probs[i % len(attack_probs)]
        
        if attack_type == 'invert':
            attack_configs[mal_idx] = AttackConfigGenerator.generate_byzantine_config('invert', attack_prob)
        elif attack_type == 'random':
            attack_configs[mal_idx] = AttackConfigGenerator.generate_fake_update_config('random', attack_prob)
        else:  # scale
            attack_configs[mal_idx] = AttackConfigGenerator.generate_byzantine_config('scale', attack_prob)
    
    # 为诚实客户端设置None
    for i in range(n_clients):
        if i not in malicious_indices:
            attack_configs[i] = None
    
    # 创建POL-martFL集成器
    integrator = POLMartFLIntegrator(
        device=device,
        pol_enabled=True,
        distance_metric='cosine',
        fault_tolerance_ratio=0.33
    )
    
    # 初始化全局模型
    global_model = create_realistic_model()
    
    # 记录实验结果
    round_results = []
    cumulative_stats = {
        'total_selections': 0,
        'total_honest_selected': 0,
        'total_malicious_selected': 0,
        'total_honest_rejected': 0,
        'total_malicious_rejected': 0
    }
    
    print(f"\n{'='*60}")
    print(f"Multi-Round Federated Learning")
    print(f"{'='*60}")
    
    for round_num in range(n_rounds):
        print(f"\n--- Communication Round {round_num + 1}/{n_rounds} ---")
        
        # 选择参与的客户端
        n_participants = int(n_clients * participation_rate)
        participating_clients = random.sample(range(n_clients), n_participants)
        participating_clients.sort()
        
        participating_honest = [c for c in participating_clients if c not in malicious_indices]
        participating_malicious = [c for c in participating_clients if c in malicious_indices]
        
        print(f"  Participating clients: {participating_clients}")
        print(f"  Honest: {participating_honest} ({len(participating_honest)})")
        print(f"  Malicious: {participating_malicious} ({len(participating_malicious)})")
        
        # 客户端训练
        client_models = []
        client_pol_proofs = []
        client_ids = []
        
        for client_id in participating_clients:
            is_malicious = client_id in malicious_indices
            
            # 创建客户端模型（从全局模型初始化）
            client_model = create_realistic_model()
            client_model.load_state_dict(global_model.state_dict())
            
            # 创建数据加载器
            dataset = client_datasets[client_id]
            dataloader = torch.utils.data.DataLoader(dataset, batch_size=25, shuffle=True)
            
            # 使用POL训练器
            pol_trainer = POLMartFLTrainer(
                client_id=client_id,
                model=client_model,
                device=device,
                pol_enabled=True,
                attack_config=attack_configs[client_id]
            )
            
            optimizer = optim.SGD(client_model.parameters(), lr=learning_rate)
            
            training_result = pol_trainer.train_with_pol(
                dataloader=dataloader,
                criterion=criterion,
                optimizer=optimizer,
                epochs=local_epochs
            )
            
            client_models.append(training_result['model'])
            client_pol_proofs.append(training_result['pol_proof'])
            client_ids.append(client_id)
        
        # POL-martFL集成选择
        selected_client_ids, selection_results = integrator.integrated_client_selection(
            client_models=client_models,
            client_pol_proofs=client_pol_proofs,
            client_ids=client_ids,
            test_dataloader=test_dataloader,
            criterion=criterion
        )
        
        # 分析本轮结果
        honest_clients = set(participating_honest)
        malicious_clients = set(participating_malicious)
        selected_clients = set(selected_client_ids)
        
        honest_selected = len(honest_clients & selected_clients)
        malicious_selected = len(malicious_clients & selected_clients)
        honest_rejected = len(honest_clients - selected_clients)
        malicious_rejected = len(malicious_clients - selected_clients)
        
        # 计算本轮指标
        precision = honest_selected / (honest_selected + malicious_selected) if (honest_selected + malicious_selected) > 0 else 0
        recall = honest_selected / (honest_selected + honest_rejected) if (honest_selected + honest_rejected) > 0 else 0
        f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
        
        print(f"  Selection results: {selected_client_ids}")
        print(f"  Honest selected: {honest_selected}/{len(honest_clients)}")
        print(f"  Malicious selected: {malicious_selected}/{len(malicious_clients)}")
        print(f"  Precision: {precision:.1%}, Recall: {recall:.1%}, F1: {f1_score:.3f}")
        
        # 更新累积统计
        cumulative_stats['total_selections'] += len(selected_client_ids)
        cumulative_stats['total_honest_selected'] += honest_selected
        cumulative_stats['total_malicious_selected'] += malicious_selected
        cumulative_stats['total_honest_rejected'] += honest_rejected
        cumulative_stats['total_malicious_rejected'] += malicious_rejected
        
        # 记录本轮结果
        round_results.append({
            'round': round_num + 1,
            'participating_clients': participating_clients,
            'selected_clients': selected_client_ids,
            'honest_selected': honest_selected,
            'malicious_selected': malicious_selected,
            'precision': precision,
            'recall': recall,
            'f1_score': f1_score
        })
        
        # 联邦聚合（使用选中的客户端）
        if selected_client_ids:
            selected_models = [client_models[client_ids.index(cid)] for cid in selected_client_ids]
            global_model = federated_averaging(selected_models)
            print(f"  Global model updated with {len(selected_models)} clients")
        else:
            print(f"  No clients selected, global model unchanged")
    
    # 计算总体统计
    total_honest = cumulative_stats['total_honest_selected'] + cumulative_stats['total_honest_rejected']
    total_malicious = cumulative_stats['total_malicious_selected'] + cumulative_stats['total_malicious_rejected']
    
    overall_precision = cumulative_stats['total_honest_selected'] / cumulative_stats['total_selections'] if cumulative_stats['total_selections'] > 0 else 0
    overall_recall = cumulative_stats['total_honest_selected'] / total_honest if total_honest > 0 else 0
    overall_f1 = 2 * (overall_precision * overall_recall) / (overall_precision + overall_recall) if (overall_precision + overall_recall) > 0 else 0
    
    malicious_detection_rate = cumulative_stats['total_malicious_rejected'] / total_malicious if total_malicious > 0 else 0
    
    print(f"\n{'='*60}")
    print(f"Multi-Round Results Summary")
    print(f"{'='*60}")
    
    print(f"\nOverall Statistics ({n_rounds} rounds):")
    print(f"  Total honest clients: {total_honest}")
    print(f"  Total malicious clients: {total_malicious}")
    print(f"  Total selections: {cumulative_stats['total_selections']}")
    
    print(f"\nOverall Performance:")
    print(f"  Precision: {overall_precision:.1%}")
    print(f"  Recall: {overall_recall:.1%}")
    print(f"  F1-Score: {overall_f1:.3f}")
    print(f"  Malicious Detection Rate: {malicious_detection_rate:.1%}")
    
    print(f"\nDetailed Breakdown:")
    print(f"  Honest Selected: {cumulative_stats['total_honest_selected']}")
    print(f"  Honest Rejected: {cumulative_stats['total_honest_rejected']}")
    print(f"  Malicious Selected: {cumulative_stats['total_malicious_selected']}")
    print(f"  Malicious Rejected: {cumulative_stats['total_malicious_rejected']}")
    
    # 分析每轮趋势
    print(f"\nRound-by-Round Analysis:")
    avg_precision = np.mean([r['precision'] for r in round_results])
    avg_recall = np.mean([r['recall'] for r in round_results])
    avg_f1 = np.mean([r['f1_score'] for r in round_results])
    
    print(f"  Average Precision: {avg_precision:.1%}")
    print(f"  Average Recall: {avg_recall:.1%}")
    print(f"  Average F1-Score: {avg_f1:.3f}")
    
    # 性能评估
    print(f"\n{'='*60}")
    print(f"Performance Assessment")
    print(f"{'='*60}")
    
    if overall_precision >= 0.9 and overall_recall >= 0.7:
        print(f"🎉 Excellent multi-round performance!")
    elif overall_precision >= 0.8 and overall_recall >= 0.6:
        print(f"✅ Good multi-round performance")
    elif overall_precision >= 0.7 and overall_recall >= 0.5:
        print(f"⚠️  Moderate multi-round performance")
    else:
        print(f"❌ Poor multi-round performance - needs improvement")
    
    # 问题诊断
    if overall_recall < 0.6:
        print(f"⚠️  High false negative rate: {cumulative_stats['total_honest_rejected']} honest clients rejected")
    
    if cumulative_stats['total_malicious_selected'] > total_malicious * 0.2:
        print(f"⚠️  High false positive rate: {cumulative_stats['total_malicious_selected']} malicious clients selected")
    
    return {
        'overall_precision': overall_precision,
        'overall_recall': overall_recall,
        'overall_f1': overall_f1,
        'malicious_detection_rate': malicious_detection_rate,
        'round_results': round_results,
        'cumulative_stats': cumulative_stats
    }


def main():
    """主函数"""
    try:
        print("Starting Large Scale Multi-Round POL Integration Test...")
        
        results = test_large_scale_multi_round()
        
        print("\n" + "🎯" + "="*78)
        print("LARGE SCALE MULTI-ROUND TEST COMPLETED!")
        print("="*80)
        
        print(f"\n📊 Final Assessment:")
        print(f"   • Overall Precision: {results['overall_precision']:.1%}")
        print(f"   • Overall Recall: {results['overall_recall']:.1%}")
        print(f"   • Overall F1-Score: {results['overall_f1']:.3f}")
        print(f"   • Malicious Detection Rate: {results['malicious_detection_rate']:.1%}")
        
        if results['overall_f1'] >= 0.7:
            print(f"\n🎉 Multi-round POL integration is working well!")
        else:
            print(f"\n⚠️  Multi-round performance needs optimization")
        
        return 0
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == '__main__':
    exit(main())
