#!/usr/bin/env python3
"""
Test Enhanced POL Verifier
测试增强的POL验证器
"""

import os
import sys
import torch
import torch.nn as nn
import numpy as np
import time

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.join(current_dir, '..', 'src', 'integration'))
sys.path.append(os.path.join(current_dir, '..', 'src', 'attacks'))

from enhanced_pol_verifier import EnhancedPOLVerifier
from byzantine_pol_verifier import ByzantinePOLVerifier
from pol_martfl_trainer import POLMartFLTrainer
from attack_simulator import AttackConfigGenerator


def create_test_model():
    """创建测试模型"""
    return nn.Sequential(
        nn.Flatten(),
        nn.Linear(10, 32),
        nn.ReLU(),
        nn.Linear(32, 16),
        nn.<PERSON><PERSON><PERSON>(),
        nn.Linear(16, 4)
    )


def create_test_dataloader(size=100):
    """创建测试数据加载器"""
    data = torch.randn(size, 10)
    labels = torch.randint(0, 4, (size,))
    dataset = torch.utils.data.TensorDataset(data, labels)
    return torch.utils.data.DataLoader(dataset, batch_size=16, shuffle=True)


def generate_test_proofs(n_clients=20, malicious_ratio=0.3):
    """生成测试POL证明"""
    print(f"Generating {n_clients} test POL proofs...")
    
    device = torch.device('cpu')
    n_malicious = int(n_clients * malicious_ratio)
    
    proofs = []
    ground_truth = []
    
    for i in range(n_clients):
        is_malicious = i < n_malicious
        ground_truth.append(not is_malicious)  # True for honest
        
        # 配置攻击
        if is_malicious:
            attack_config = {
                'byzantine_attack': True,
                'attack_type': np.random.choice(['random', 'invert', 'const']),
                'attack_prob': np.random.uniform(0.7, 1.0)
            }
        else:
            attack_config = {}
        
        # 创建初始模型和数据
        initial_model = create_test_model()
        dataloader = create_test_dataloader(size=80 if is_malicious else 120)

        # 创建训练器
        trainer = POLMartFLTrainer(
            client_id=i,
            model=initial_model,
            device=device,
            pol_enabled=True,
            attack_config=attack_config
        )

        # 训练并生成POL证明
        criterion = nn.CrossEntropyLoss()
        optimizer = torch.optim.SGD(initial_model.parameters(), lr=0.02)

        training_result = trainer.train_with_pol(
            dataloader=dataloader,
            criterion=criterion,
            optimizer=optimizer,
            epochs=2
        )

        # 提取POL证明
        if isinstance(training_result, dict) and 'pol_proof' in training_result:
            pol_proof = training_result['pol_proof']
        else:
            pol_proof = training_result

        trained_model = trainer.model

        proofs.append(pol_proof)
        
        # 检查POL证明结构
        if pol_proof and isinstance(pol_proof, dict) and 'W' in pol_proof:
            weight_count = len(pol_proof['W']) if pol_proof['W'] else 0
            print(f"  Client {i}: {'Malicious' if is_malicious else 'Honest'} - "
                  f"Proof generated with {weight_count} weight checkpoints")
        else:
            print(f"  Client {i}: {'Malicious' if is_malicious else 'Honest'} - "
                  f"Proof structure issue - Type: {type(pol_proof)}")
            if isinstance(pol_proof, dict):
                print(f"    Available keys: {list(pol_proof.keys())}")
            # 尝试修复证明结构
            if isinstance(pol_proof, dict) and 'pol_proof' in pol_proof:
                pol_proof = pol_proof['pol_proof']
                proofs[-1] = pol_proof  # 更新列表中的证明
    
    return proofs, ground_truth


def test_verifier_performance(verifier, proofs, ground_truth, verifier_name):
    """测试验证器性能"""
    print(f"\n🔍 Testing {verifier_name}...")
    
    start_time = time.time()
    
    # 检查验证器类型
    if isinstance(verifier, EnhancedPOLVerifier):
        # 增强验证器
        result = verifier.verify_all_clients(proofs)
        verification_results = result['verification_results']
        verification_time = result['verification_time']
    else:
        # 原始验证器
        result = verifier.verify_all_clients(proofs)
        if isinstance(result, dict):
            verification_results = result['verification_results']
            verification_time = result.get('verification_time', time.time() - start_time)
        else:
            verification_results = result
            verification_time = time.time() - start_time
    
    # 计算性能指标
    predictions = verification_results
    true_labels = ground_truth
    
    tp = sum(1 for p, t in zip(predictions, true_labels) if p and t)
    tn = sum(1 for p, t in zip(predictions, true_labels) if not p and not t)
    fp = sum(1 for p, t in zip(predictions, true_labels) if p and not t)
    fn = sum(1 for p, t in zip(predictions, true_labels) if not p and t)
    
    precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
    recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
    f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0
    accuracy = (tp + tn) / (tp + tn + fp + fn)
    
    print(f"  ✅ Results for {verifier_name}:")
    print(f"    Precision: {precision:.3f}")
    print(f"    Recall: {recall:.3f}")
    print(f"    F1-Score: {f1_score:.3f}")
    print(f"    Accuracy: {accuracy:.3f}")
    print(f"    Verification Time: {verification_time:.3f}s")
    print(f"    Confusion Matrix: TP={tp}, TN={tn}, FP={fp}, FN={fn}")
    
    return {
        'precision': precision,
        'recall': recall,
        'f1_score': f1_score,
        'accuracy': accuracy,
        'verification_time': verification_time,
        'confusion_matrix': {'tp': tp, 'tn': tn, 'fp': fp, 'fn': fn}
    }


def run_comparison_test():
    """运行对比测试"""
    print("🚀 Starting Enhanced POL Verifier Comparison Test")
    print("=" * 60)
    
    device = torch.device('cpu')
    
    # 生成测试数据
    print("\n📊 Generating test data...")
    proofs, ground_truth = generate_test_proofs(n_clients=24, malicious_ratio=0.25)
    
    honest_count = sum(ground_truth)
    malicious_count = len(ground_truth) - honest_count
    print(f"Generated {len(proofs)} proofs: {honest_count} honest, {malicious_count} malicious")
    
    # 创建验证器
    print("\n🔧 Creating verifiers...")
    
    # 原始验证器
    original_verifier = ByzantinePOLVerifier(
        device=device,
        distance_metric='cosine',
        fault_tolerance_ratio=0.33
    )
    
    # 增强验证器
    enhanced_verifier = EnhancedPOLVerifier(
        device=device,
        distance_metric='cosine',
        target_recall=0.7,
        target_precision=0.9
    )
    
    # 测试验证器
    results = {}
    
    # 测试原始验证器
    results['Original'] = test_verifier_performance(
        original_verifier, proofs, ground_truth, "Original Byzantine Verifier"
    )
    
    # 测试增强验证器
    results['Enhanced'] = test_verifier_performance(
        enhanced_verifier, proofs, ground_truth, "Enhanced POL Verifier"
    )
    
    # 对比结果
    print("\n📈 Performance Comparison:")
    print("=" * 60)
    print(f"{'Metric':<15} {'Original':<12} {'Enhanced':<12} {'Improvement':<12}")
    print("-" * 60)
    
    metrics = ['precision', 'recall', 'f1_score', 'accuracy']
    for metric in metrics:
        original_val = results['Original'][metric]
        enhanced_val = results['Enhanced'][metric]
        improvement = ((enhanced_val - original_val) / original_val * 100) if original_val > 0 else 0
        
        print(f"{metric.capitalize():<15} {original_val:<12.3f} {enhanced_val:<12.3f} {improvement:+.1f}%")
    
    # 时间对比
    original_time = results['Original']['verification_time']
    enhanced_time = results['Enhanced']['verification_time']
    time_change = ((enhanced_time - original_time) / original_time * 100) if original_time > 0 else 0
    
    print(f"{'Time (s)':<15} {original_time:<12.3f} {enhanced_time:<12.3f} {time_change:+.1f}%")
    
    # 总结
    print("\n🎯 Summary:")
    if results['Enhanced']['f1_score'] > results['Original']['f1_score']:
        print("✅ Enhanced verifier shows improved performance!")
        improvement = (results['Enhanced']['f1_score'] - results['Original']['f1_score']) * 100
        print(f"   F1-Score improvement: +{improvement:.1f} percentage points")
    else:
        print("⚠️  Enhanced verifier needs further optimization")
    
    if results['Enhanced']['recall'] > results['Original']['recall']:
        recall_improvement = (results['Enhanced']['recall'] - results['Original']['recall']) * 100
        print(f"✅ Recall improvement: +{recall_improvement:.1f} percentage points")
    
    return results


def main():
    """主函数"""
    try:
        results = run_comparison_test()
        
        print("\n🎉 Test completed successfully!")
        print("📊 Enhanced POL verifier evaluation finished.")
        
        return results
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    main()
