#!/usr/bin/env python3
"""
Quick Comparison Test
快速对比测试：原始系统 vs 增强系统
"""

import os
import sys
import torch
import torch.nn as nn
import numpy as np
import time

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.join(current_dir, '..', 'src', 'integration'))
sys.path.append(os.path.join(current_dir, '..', 'src', 'attacks'))

from pol_martfl_trainer import POLMartFLTrainer
from pol_martfl_integrator import POLMartFLIntegrator
from simple_enhanced_pol_verifier import SimpleEnhancedPOLVerifier


def create_test_model():
    """创建测试模型"""
    return nn.Sequential(
        nn.Flatten(),
        nn.Linear(10, 32),
        nn.ReLU(),
        nn.Linear(32, 16),
        nn.ReLU(),
        nn.Linear(16, 4)
    )


def create_test_dataloader(size=100):
    """创建测试数据加载器"""
    data = torch.randn(size, 10)
    labels = torch.randint(0, 4, (size,))
    dataset = torch.utils.data.TensorDataset(data, labels)
    return torch.utils.data.DataLoader(dataset, batch_size=16, shuffle=True)


def generate_clients_and_proofs(n_clients=20, malicious_ratio=0.3):
    """生成客户端和POL证明"""
    device = torch.device('cpu')
    n_malicious = int(n_clients * malicious_ratio)
    
    clients = []
    proofs = []
    ground_truth = []
    
    for i in range(n_clients):
        is_malicious = i < n_malicious
        ground_truth.append(not is_malicious)  # True for honest
        
        # 配置攻击
        if is_malicious:
            attack_config = {
                'byzantine_attack': True,
                'attack_type': np.random.choice(['random', 'invert', 'const']),
                'attack_prob': np.random.uniform(0.7, 1.0)
            }
        else:
            attack_config = {}
        
        clients.append({
            'id': i,
            'is_malicious': is_malicious,
            'attack_config': attack_config
        })
        
        # 创建训练器并生成POL证明
        trainer = POLMartFLTrainer(
            client_id=i,
            model=create_test_model(),
            device=device,
            pol_enabled=True,
            attack_config=attack_config
        )
        
        criterion = nn.CrossEntropyLoss()
        optimizer = torch.optim.SGD(trainer.model.parameters(), lr=0.01)
        
        training_result = trainer.train_with_pol(
            dataloader=create_test_dataloader(size=80 if is_malicious else 120),
            criterion=criterion,
            optimizer=optimizer,
            epochs=2
        )
        
        # 提取POL证明
        if isinstance(training_result, dict) and 'pol_proof' in training_result:
            pol_proof = training_result['pol_proof']
        else:
            pol_proof = training_result
        
        proofs.append(pol_proof)
    
    return clients, proofs, ground_truth


def test_system(system_name, integrator, clients, proofs, ground_truth):
    """测试系统性能"""
    print(f"\n🔍 Testing {system_name}...")
    
    start_time = time.time()
    
    # 创建模型列表
    models = [create_test_model() for _ in clients]
    client_ids = [c['id'] for c in clients]
    
    # 执行客户端选择
    selected_clients, selection_details = integrator.integrated_client_selection(
        client_models=models,
        client_pol_proofs=proofs,
        client_ids=client_ids,
        test_dataloader=create_test_dataloader(),
        criterion=nn.CrossEntropyLoss()
    )
    
    # 计算性能指标
    predictions = []
    true_labels = []
    
    for i, client in enumerate(clients):
        is_selected = client['id'] in selected_clients
        is_honest = ground_truth[i]
        
        predictions.append(is_selected)
        true_labels.append(is_honest)
    
    # 计算混淆矩阵
    tp = sum(1 for p, t in zip(predictions, true_labels) if p and t)
    tn = sum(1 for p, t in zip(predictions, true_labels) if not p and not t)
    fp = sum(1 for p, t in zip(predictions, true_labels) if p and not t)
    fn = sum(1 for p, t in zip(predictions, true_labels) if not p and t)
    
    # 计算指标
    precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
    recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
    f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0
    accuracy = (tp + tn) / (tp + tn + fp + fn)
    
    test_time = time.time() - start_time
    
    print(f"  ✅ Results:")
    print(f"    Precision: {precision:.3f}")
    print(f"    Recall: {recall:.3f}")
    print(f"    F1-Score: {f1_score:.3f}")
    print(f"    Accuracy: {accuracy:.3f}")
    print(f"    Test Time: {test_time:.3f}s")
    print(f"    Selected: {len(selected_clients)}/{len(clients)} clients")
    print(f"    Confusion Matrix: TP={tp}, TN={tn}, FP={fp}, FN={fn}")
    
    return {
        'precision': precision,
        'recall': recall,
        'f1_score': f1_score,
        'accuracy': accuracy,
        'test_time': test_time,
        'selected_count': len(selected_clients),
        'confusion_matrix': {'tp': tp, 'tn': tn, 'fp': fp, 'fn': fn}
    }


def run_quick_comparison():
    """运行快速对比测试"""
    print("🚀 Quick POL-martFL Comparison Test")
    print("=" * 60)
    
    device = torch.device('cpu')
    
    # 生成测试数据
    print("\n📊 Generating test data...")
    clients, proofs, ground_truth = generate_clients_and_proofs(n_clients=20, malicious_ratio=0.3)
    
    honest_count = sum(ground_truth)
    malicious_count = len(ground_truth) - honest_count
    print(f"Generated {len(proofs)} clients: {honest_count} honest, {malicious_count} malicious")
    
    # 创建系统
    print("\n🔧 Creating systems...")
    
    # 原始系统
    original_integrator = POLMartFLIntegrator(
        device=device,
        pol_enabled=True,
        distance_metric='cosine'
    )
    
    # 增强系统
    enhanced_verifier = SimpleEnhancedPOLVerifier(
        device=device,
        target_recall=0.7,
        target_precision=0.9
    )
    
    enhanced_integrator = POLMartFLIntegrator(
        device=device,
        pol_enabled=True,
        distance_metric='cosine'
    )
    enhanced_integrator.pol_verifier = enhanced_verifier
    
    # 测试系统
    results = {}
    
    results['Original'] = test_system(
        "Original System", original_integrator, clients, proofs, ground_truth
    )
    
    results['Enhanced'] = test_system(
        "Enhanced System", enhanced_integrator, clients, proofs, ground_truth
    )
    
    # 对比结果
    print("\n📈 Performance Comparison:")
    print("=" * 80)
    print(f"{'Metric':<15} {'Original':<12} {'Enhanced':<12} {'Improvement':<15}")
    print("-" * 80)
    
    metrics = ['precision', 'recall', 'f1_score', 'accuracy']
    for metric in metrics:
        original_val = results['Original'][metric]
        enhanced_val = results['Enhanced'][metric]
        improvement = ((enhanced_val - original_val) / original_val * 100) if original_val > 0 else 0
        
        print(f"{metric.capitalize():<15} {original_val:<12.3f} {enhanced_val:<12.3f} {improvement:+.1f}%")
    
    # 时间对比
    original_time = results['Original']['test_time']
    enhanced_time = results['Enhanced']['test_time']
    time_change = ((enhanced_time - original_time) / original_time * 100) if original_time > 0 else 0
    
    print(f"{'Time (s)':<15} {original_time:<12.3f} {enhanced_time:<12.3f} {time_change:+.1f}%")
    
    # 总结
    print("\n🎯 Summary:")
    print("=" * 60)
    
    if results['Enhanced']['f1_score'] > results['Original']['f1_score']:
        improvement = (results['Enhanced']['f1_score'] - results['Original']['f1_score']) * 100
        print(f"✅ Enhanced system shows improved F1-Score: +{improvement:.1f} percentage points")
    else:
        decline = (results['Original']['f1_score'] - results['Enhanced']['f1_score']) * 100
        print(f"⚠️  Enhanced system F1-Score declined: -{decline:.1f} percentage points")
    
    if results['Enhanced']['recall'] > results['Original']['recall']:
        recall_improvement = (results['Enhanced']['recall'] - results['Original']['recall']) * 100
        print(f"✅ Enhanced system improved recall: +{recall_improvement:.1f} percentage points")
    else:
        recall_decline = (results['Original']['recall'] - results['Enhanced']['recall']) * 100
        print(f"⚠️  Enhanced system recall declined: -{recall_decline:.1f} percentage points")
    
    if results['Enhanced']['precision'] > results['Original']['precision']:
        precision_improvement = (results['Enhanced']['precision'] - results['Original']['precision']) * 100
        print(f"✅ Enhanced system improved precision: +{precision_improvement:.1f} percentage points")
    
    # 保存结果
    results_dir = os.path.join(os.path.dirname(__file__), '..', 'results')
    os.makedirs(results_dir, exist_ok=True)
    
    summary_path = os.path.join(results_dir, 'quick_comparison_summary.txt')
    with open(summary_path, 'w') as f:
        f.write("POL-martFL Quick Comparison Test Results\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"Test Configuration:\n")
        f.write(f"  Total Clients: {len(clients)}\n")
        f.write(f"  Honest Clients: {honest_count}\n")
        f.write(f"  Malicious Clients: {malicious_count}\n")
        f.write(f"  Malicious Ratio: {malicious_count/len(clients):.1%}\n\n")
        
        f.write("Performance Metrics:\n")
        f.write("-" * 30 + "\n")
        for metric in metrics:
            original_val = results['Original'][metric]
            enhanced_val = results['Enhanced'][metric]
            improvement = ((enhanced_val - original_val) / original_val * 100) if original_val > 0 else 0
            f.write(f"{metric.capitalize():<15} Original: {original_val:.3f}, Enhanced: {enhanced_val:.3f} ({improvement:+.1f}%)\n")
    
    print(f"\n📁 Results saved to: {summary_path}")
    
    return results


def main():
    """主函数"""
    try:
        results = run_quick_comparison()
        print("\n🎉 Quick comparison test completed successfully!")
        return results
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    main()
