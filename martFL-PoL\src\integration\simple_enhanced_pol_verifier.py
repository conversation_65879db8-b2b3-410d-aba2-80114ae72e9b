"""
Simple Enhanced POL Verifier
简化的增强POL验证器，不依赖sklearn
"""

import torch
import torch.nn as nn
import numpy as np
import time
from typing import Dict, List, Tuple, Optional, Any


class SimpleEnhancedPOLVerifier:
    """
    简化的增强POL验证器
    使用多种策略提高验证准确性，平衡精确度和召回率
    """
    
    def __init__(self,
                 device: torch.device,
                 distance_metric: str = 'cosine',
                 target_recall: float = 0.7,
                 target_precision: float = 0.9):
        """
        初始化增强POL验证器

        Args:
            device: 计算设备
            distance_metric: 距离度量
            target_recall: 目标召回率
            target_precision: 目标精确度
        """
        self.device = device
        self.distance_metric = distance_metric
        self.target_recall = target_recall
        self.target_precision = target_precision

        # 历史信息
        self.threshold_history = []
        self.performance_history = []
        
        # 验证统计
        self.verification_stats = {
            'total_verifications': 0,
            'successful_verifications': 0,
            'failed_verifications': 0,
            'verification_times': [],
            'threshold_evolution': []
        }
    
    def verify_all_clients(self, client_pol_proofs: List[Optional[Dict[str, Any]]]) -> Dict[str, Any]:
        """
        验证所有客户端的POL证明 - 增强方法
        
        Args:
            client_pol_proofs: 所有客户端的POL证明列表
            
        Returns:
            验证结果字典
        """
        start_time = time.time()
        
        print(f"[Simple Enhanced POL Verifier] Starting verification of {len(client_pol_proofs)} clients...")
        
        # 1. 基本完整性检查
        valid_proofs = []
        valid_indices = []
        
        for i, proof in enumerate(client_pol_proofs):
            if proof and self._basic_integrity_check(proof):
                valid_proofs.append(proof)
                valid_indices.append(i)
        
        print(f"  Basic integrity check: {len(valid_proofs)}/{len(client_pol_proofs)} clients passed")
        
        if len(valid_proofs) < 2:
            print(f"  Insufficient valid proofs for analysis")
            return self._create_verification_result(client_pol_proofs, [False] * len(client_pol_proofs))
        
        # 2. 提取增强特征
        client_features = []
        for proof in valid_proofs:
            features = self._extract_enhanced_features(proof)
            client_features.append(features)
        
        # 3. 多策略验证
        verification_results = self._multi_strategy_verification(client_features)
        
        # 4. 构建最终结果
        final_results = [False] * len(client_pol_proofs)
        pol_base_weights = [0.0] * len(client_pol_proofs)
        
        for i, valid_idx in enumerate(valid_indices):
            final_results[valid_idx] = verification_results['individual_results'][i]
            if verification_results['individual_results'][i]:
                pol_base_weights[valid_idx] = verification_results['pol_weights'][i]
        
        verification_time = time.time() - start_time
        self.verification_stats['verification_times'].append(verification_time)
        
        verified_count = sum(final_results)
        rejected_count = len(final_results) - verified_count
        
        print(f"  Enhanced verification completed: {verified_count} verified, {rejected_count} rejected")
        print(f"  Verification time: {verification_time:.3f}s")
        
        return {
            'verification_results': final_results,
            'pol_base_weights': pol_base_weights,
            'verification_details': verification_results,
            'verification_time': verification_time,
            'threshold_used': verification_results['threshold_used']
        }
    
    def _basic_integrity_check(self, proof: Dict[str, Any]) -> bool:
        """基本完整性检查"""
        # 检查POL证明的标准结构 (W, I, H, M)
        required_keys = ['W', 'I', 'H', 'M']
        return all(key in proof for key in required_keys)
    
    def _extract_enhanced_features(self, proof: Dict[str, Any]) -> Dict[str, float]:
        """
        提取增强的POL特征
        包括多种统计特征和模式识别
        """
        weight_checkpoints = proof['W']  # POL证明中的权重序列
        
        # 计算权重变化
        weight_changes = []
        for i in range(1, len(weight_checkpoints)):
            prev_checkpoint = weight_checkpoints[i-1]
            curr_checkpoint = weight_checkpoints[i]
            
            # 提取权重字典
            prev_weights = prev_checkpoint.get('weights', {})
            curr_weights = curr_checkpoint.get('weights', {})
            
            # 计算L2距离
            if isinstance(prev_weights, dict) and isinstance(curr_weights, dict):
                change = 0.0
                for key in prev_weights.keys():
                    if key in curr_weights:
                        prev_param = prev_weights[key]
                        curr_param = curr_weights[key]
                        
                        # 处理OrderedDict或其他嵌套结构
                        if hasattr(prev_param, 'items') and hasattr(curr_param, 'items'):
                            # 这是一个参数字典（如OrderedDict）
                            for param_name in prev_param.keys():
                                if param_name in curr_param:
                                    prev_tensor = prev_param[param_name]
                                    curr_tensor = curr_param[param_name]
                                    
                                    # 确保是张量
                                    if not torch.is_tensor(prev_tensor):
                                        prev_tensor = torch.tensor(prev_tensor)
                                    if not torch.is_tensor(curr_tensor):
                                        curr_tensor = torch.tensor(curr_tensor)
                                    
                                    # 确保张量是浮点类型
                                    if prev_tensor.dtype not in [torch.float32, torch.float64]:
                                        prev_tensor = prev_tensor.float()
                                    if curr_tensor.dtype not in [torch.float32, torch.float64]:
                                        curr_tensor = curr_tensor.float()
                                    
                                    change += torch.norm(curr_tensor - prev_tensor).item() ** 2
                        else:
                            # 直接是张量
                            if not torch.is_tensor(prev_param):
                                prev_param = torch.tensor(prev_param)
                            if not torch.is_tensor(curr_param):
                                curr_param = torch.tensor(curr_param)
                            
                            # 确保张量是浮点类型
                            if prev_param.dtype not in [torch.float32, torch.float64]:
                                prev_param = prev_param.float()
                            if curr_param.dtype not in [torch.float32, torch.float64]:
                                curr_param = curr_param.float()
                            
                            change += torch.norm(curr_param - prev_param).item() ** 2
                
                weight_changes.append(np.sqrt(change))
        
        if not weight_changes:
            weight_changes = [0.0]
        
        # 基础统计特征
        avg_change = np.mean(weight_changes)
        std_change = np.std(weight_changes) if len(weight_changes) > 1 else 0.0
        max_change = np.max(weight_changes)
        min_change = np.min(weight_changes)
        
        # 高级特征
        change_trend = self._calculate_trend(weight_changes)
        change_consistency = 1.0 / (1.0 + std_change) if std_change > 0 else 1.0
        change_smoothness = self._calculate_smoothness(weight_changes)
        
        return {
            'avg_weight_change': avg_change,
            'std_weight_change': std_change,
            'max_weight_change': max_change,
            'min_weight_change': min_change,
            'change_trend': change_trend,
            'change_consistency': change_consistency,
            'change_smoothness': change_smoothness,
            'num_checkpoints': len(weight_checkpoints)
        }
    
    def _calculate_trend(self, changes: List[float]) -> float:
        """计算变化趋势"""
        if len(changes) < 2:
            return 0.0
        
        x = np.arange(len(changes))
        y = np.array(changes)
        
        # 简单线性回归计算趋势
        slope = np.corrcoef(x, y)[0, 1] if len(changes) > 1 else 0.0
        return slope if not np.isnan(slope) else 0.0
    
    def _calculate_smoothness(self, changes: List[float]) -> float:
        """计算变化平滑度"""
        if len(changes) < 3:
            return 1.0
        
        # 计算二阶差分的方差
        second_diffs = []
        for i in range(2, len(changes)):
            second_diff = changes[i] - 2 * changes[i-1] + changes[i-2]
            second_diffs.append(abs(second_diff))
        
        smoothness = 1.0 / (1.0 + np.mean(second_diffs)) if second_diffs else 1.0
        return smoothness

    def _multi_strategy_verification(self, client_features: List[Dict[str, float]]) -> Dict[str, Any]:
        """
        多策略验证方法
        结合统计分析和自适应阈值（简化版，不使用聚类）
        """
        print(f"    Starting multi-strategy verification...")

        # 策略1：统计分析
        statistical_results = self._statistical_analysis_verification(client_features)

        # 策略2：自适应阈值
        adaptive_results = self._adaptive_threshold_verification(client_features)

        # 策略融合（简化版）
        final_results = self._fuse_verification_strategies(
            statistical_results, adaptive_results, client_features
        )

        return final_results

    def _statistical_analysis_verification(self, client_features: List[Dict[str, float]]) -> Dict[str, Any]:
        """统计分析验证策略"""
        changes = [f['avg_weight_change'] for f in client_features]
        changes_array = np.array(changes)

        # 异常值检测 - IQR方法
        q25 = np.percentile(changes_array, 25)
        q75 = np.percentile(changes_array, 75)
        iqr = q75 - q25
        outlier_threshold = q75 + 1.5 * iqr

        # Z-score方法
        mean_change = np.mean(changes_array)
        std_change = np.std(changes_array)
        z_scores = np.abs((changes_array - mean_change) / std_change) if std_change > 0 else np.zeros_like(changes_array)

        # 结合两种方法
        iqr_results = changes_array <= outlier_threshold
        zscore_results = z_scores <= 2.5  # 2.5个标准差

        statistical_results = iqr_results & zscore_results

        return {
            'results': statistical_results.tolist(),
            'threshold': outlier_threshold,
            'method': 'statistical',
            'pass_rate': np.mean(statistical_results)
        }

    def _adaptive_threshold_verification(self, client_features: List[Dict[str, float]]) -> Dict[str, Any]:
        """自适应阈值验证策略"""
        changes = [f['avg_weight_change'] for f in client_features]
        changes_array = np.array(changes)

        # 候选阈值策略
        candidate_thresholds = {
            'median_based': np.median(changes_array) + 0.5 * np.std(changes_array),
            'percentile_75': np.percentile(changes_array, 75),
            'percentile_85': np.percentile(changes_array, 85),
            'mean_plus_std': np.mean(changes_array) + np.std(changes_array),
            'otsu_like': self._calculate_otsu_threshold(changes_array)
        }

        # 选择最优阈值（目标召回率）
        best_threshold = None
        best_score = -1

        for name, threshold in candidate_thresholds.items():
            pass_rate = np.mean(changes_array <= threshold)

            # 计算与目标召回率的匹配度
            recall_score = 1.0 - abs(pass_rate - self.target_recall)

            if recall_score > best_score:
                best_score = recall_score
                best_threshold = threshold
                best_strategy = name

        adaptive_results = changes_array <= best_threshold

        return {
            'results': adaptive_results.tolist(),
            'threshold': best_threshold,
            'strategy': best_strategy,
            'method': 'adaptive',
            'pass_rate': np.mean(adaptive_results)
        }

    def _calculate_otsu_threshold(self, changes: np.ndarray) -> float:
        """计算类似Otsu的最优阈值"""
        sorted_changes = np.sort(changes)
        n = len(sorted_changes)

        best_threshold = sorted_changes[int(n * 0.75)]
        best_variance = float('inf')

        # 在50%-90%分位数范围内寻找最优分割点
        start_idx = int(n * 0.5)
        end_idx = int(n * 0.9)

        for i in range(start_idx, end_idx):
            threshold = sorted_changes[i]

            # 计算两组的加权方差
            group1 = sorted_changes[:i+1]
            group2 = sorted_changes[i+1:]

            if len(group1) > 0 and len(group2) > 0:
                w1, w2 = len(group1) / n, len(group2) / n
                var1 = np.var(group1) if len(group1) > 1 else 0
                var2 = np.var(group2) if len(group2) > 1 else 0
                weighted_var = w1 * var1 + w2 * var2

                if weighted_var < best_variance:
                    best_variance = weighted_var
                    best_threshold = threshold

        return best_threshold

    def _fuse_verification_strategies(self, statistical_results: Dict, adaptive_results: Dict,
                                    client_features: List[Dict[str, float]]) -> Dict[str, Any]:
        """
        融合多种验证策略的结果（简化版）
        """
        n_clients = len(client_features)

        # 获取各策略结果
        stat_results = np.array(statistical_results['results'])
        adaptive_results_array = np.array(adaptive_results['results'])

        # 策略权重（优化后的权重分配）
        weights = {
            'statistical': 0.5,  # 统计分析权重
            'adaptive': 0.5      # 自适应方法权重
        }

        # 加权投票
        vote_scores = (weights['statistical'] * stat_results +
                      weights['adaptive'] * adaptive_results_array)

        # 动态阈值（基于目标召回率，但更加灵活）
        sorted_indices = np.argsort(vote_scores)[::-1]  # 降序排列

        # 使用更灵活的选择策略
        threshold = 0.6  # 降低阈值，使其更宽松
        high_score_mask = vote_scores >= threshold

        # 如果高分客户端数量不足目标召回率，则补充更多客户端
        target_count = max(1, int(n_clients * self.target_recall))
        high_score_count = np.sum(high_score_mask)

        final_results = np.zeros(n_clients, dtype=bool)

        if high_score_count >= target_count:
            # 如果高分客户端足够，选择得分最高的
            final_results[sorted_indices[:target_count]] = True
        else:
            # 如果高分客户端不足，先选择所有高分客户端，再补充
            final_results[high_score_mask] = True
            remaining_count = target_count - high_score_count
            if remaining_count > 0:
                # 从剩余客户端中选择得分最高的
                remaining_indices = sorted_indices[high_score_count:high_score_count + remaining_count]
                final_results[remaining_indices] = True

        # 计算POL权重
        pol_weights = []
        for i in range(n_clients):
            if final_results[i]:
                # 基于一致性和质量计算权重
                consistency_score = (stat_results[i] + adaptive_results_array[i]) / 2.0
                quality_score = client_features[i]['change_consistency'] * client_features[i]['change_smoothness']
                pol_weight = 1.0 + 0.3 * consistency_score + 0.2 * quality_score
                pol_weights.append(pol_weight)
            else:
                pol_weights.append(0.0)

        print(f"    Strategy fusion completed:")
        print(f"      Statistical pass rate: {statistical_results['pass_rate']:.1%}")
        print(f"      Adaptive pass rate: {adaptive_results['pass_rate']:.1%}")
        print(f"      Final pass rate: {np.mean(final_results):.1%}")

        return {
            'individual_results': final_results.tolist(),
            'pol_weights': pol_weights,
            'vote_scores': vote_scores.tolist(),
            'strategy_results': {
                'statistical': statistical_results,
                'adaptive': adaptive_results
            },
            'threshold_used': adaptive_results['threshold'],
            'fusion_method': 'weighted_voting'
        }

    def _create_verification_result(self, client_pol_proofs: List, results: List[bool]) -> Dict[str, Any]:
        """创建验证结果"""
        pol_weights = [1.0 if result else 0.0 for result in results]

        return {
            'verification_results': results,
            'pol_base_weights': pol_weights,
            'verification_details': {'method': 'basic'},
            'verification_time': 0.0,
            'threshold_used': 0.0
        }
