"""
Byzantine POL Verifier
基于拜占庭容错和投票机制的POL验证器
"""

import torch
import torch.nn as nn
import numpy as np
import time
from typing import Dict, List, Tuple, Optional, Any


class ByzantinePOLVerifier:
    """
    拜占庭容错POL验证器
    使用投票机制确定验证阈值，基于群体行为识别异常客户端
    """
    
    def __init__(self,
                 device: torch.device,
                 distance_metric: str = 'cosine',
                 fault_tolerance_ratio: float = 0.33,
                 initial_threshold: float = 0.001):
        """
        初始化拜占庭POL验证器

        Args:
            device: 计算设备
            distance_metric: 距离度量 ('cosine' based on POL paper recommendation)
            fault_tolerance_ratio: 容错比例 (最多容忍33%恶意节点)
            initial_threshold: 初始验证阈值
        """
        self.device = device
        self.distance_metric = distance_metric
        self.fault_tolerance_ratio = fault_tolerance_ratio

        # 渐进调整的阈值机制
        self.current_threshold = initial_threshold
        self.threshold_history = [initial_threshold]
        self.threshold_update_weight = 0.3  # 新投票结果的权重

        # 验证统计
        self.verification_stats = {
            'total_verifications': 0,
            'successful_verifications': 0,
            'failed_verifications': 0,
            'voting_rounds': 0,
            'verification_times': [],
            'threshold_evolution': []
        }
    
    def verify_all_clients(self, client_pol_proofs: List[Optional[Dict[str, Any]]]) -> List[bool]:
        """
        验证所有客户端的POL证明 - 拜占庭容错方法
        
        Args:
            client_pol_proofs: 所有客户端的POL证明列表
            
        Returns:
            每个客户端的验证结果列表
        """
        start_time = time.time()
        
        print(f"[Byzantine POL Verifier] Starting verification of {len(client_pol_proofs)} clients...")
        
        # 1. 基本完整性检查
        valid_proofs = []
        valid_indices = []
        
        for i, proof in enumerate(client_pol_proofs):
            if proof and self._basic_integrity_check(proof):
                valid_proofs.append(proof)
                valid_indices.append(i)
        
        print(f"  Basic integrity check: {len(valid_proofs)}/{len(client_pol_proofs)} clients passed")
        
        if len(valid_proofs) < 2:
            # 如果有效证明太少，无法进行拜占庭容错分析
            print(f"  Insufficient valid proofs for Byzantine analysis")
            return [proof is not None and self._basic_integrity_check(proof) 
                   for proof in client_pol_proofs]
        
        # 2. 提取特征用于拜占庭分析
        client_features = []
        for proof in valid_proofs:
            features = self._extract_pol_features(proof)
            client_features.append(features)
        
        # 3. 二轮投票机制确定验证阈值
        verification_threshold = self._two_round_voting_threshold(client_features)

        print(f"  Voting threshold: {verification_threshold:.6f}")
        
        # 4. 简单阈值比较
        byzantine_results = self._simple_threshold_comparison(client_features, verification_threshold)
        
        # 5. 构建最终结果
        final_results = [False] * len(client_pol_proofs)
        
        for i, valid_idx in enumerate(valid_indices):
            final_results[valid_idx] = byzantine_results[i]
        
        verification_time = time.time() - start_time
        self.verification_stats['verification_times'].append(verification_time)
        self.verification_stats['total_verifications'] += len(client_pol_proofs)
        self.verification_stats['successful_verifications'] += sum(final_results)
        self.verification_stats['failed_verifications'] += len(final_results) - sum(final_results)
        self.verification_stats['voting_rounds'] += 1
        
        verified_count = sum(final_results)
        rejected_count = len(final_results) - verified_count
        
        print(f"  Byzantine verification completed: {verified_count} verified, {rejected_count} rejected")
        print(f"  Verification time: {verification_time:.3f}s")

        # 计算POL基础权重（用于传递给martFL层）
        pol_base_weights = self._calculate_pol_base_weights(client_features, final_results)

        return {
            'verification_results': final_results,
            'pol_base_weights': pol_base_weights,
            'client_features': client_features,
            'verification_time': verification_time
        }
    
    def _basic_integrity_check(self, pol_proof: Dict[str, Any]) -> bool:
        """基本完整性检查"""
        required_keys = ['W', 'I', 'H', 'M']
        
        for key in required_keys:
            if key not in pol_proof:
                return False
        
        # 检查权重序列
        weights = pol_proof['W']
        if not weights or len(weights) < 2:
            return False
        
        # 检查至少有一些非空权重
        non_null_weights = [w for w in weights if w is not None]
        if len(non_null_weights) < 2:
            return False
        
        return True
    
    def _extract_pol_features(self, pol_proof: Dict[str, Any]) -> Dict[str, float]:
        """
        提取POL特征用于拜占庭分析 - 按POL论文正确实现
        基于epoch的权重变化和最大更新选择

        Args:
            pol_proof: POL证明

        Returns:
            特征字典
        """
        weights = pol_proof['W']
        metadata = pol_proof['M']

        # 按POL论文设计：只分析检查点权重 (每个epoch一次)
        checkpoint_weights = [w['weights'] for w in weights if w.get('is_checkpoint', False)]

        if len(checkpoint_weights) < 2:
            # 如果检查点太少，返回默认特征
            return {
                'avg_weight_change': 0.0,
                'max_weight_change': 0.0,
                'min_weight_change': 0.0,
                'weight_change_variance': 0.0,
                'total_epochs': metadata.get('epochs_trained', 0),
                'weight_checkpoints': len(checkpoint_weights),
                'data_signatures': len(pol_proof['H'])
            }

        # 计算每个epoch的权重变化 (使用余弦距离)
        epoch_changes = []
        for i in range(1, len(checkpoint_weights)):
            change = self._compute_cosine_distance(checkpoint_weights[i-1], checkpoint_weights[i])
            epoch_changes.append(change)

        # 按POL论文：选择最大的权重变化进行分析
        max_change = max(epoch_changes) if epoch_changes else 0.0

        features = {
            'avg_weight_change': np.mean(epoch_changes) if epoch_changes else 0.0,
            'max_weight_change': max_change,
            'min_weight_change': min(epoch_changes) if epoch_changes else 0.0,
            'weight_change_variance': np.var(epoch_changes) if epoch_changes else 0.0,
            'total_epochs': metadata.get('epochs_trained', 0),
            'weight_checkpoints': len(checkpoint_weights),
            'data_signatures': len(pol_proof['H'])
        }

        return features
    
    def _real_voting_threshold(self, client_features: List[Dict[str, float]]) -> float:
        """
        真正的投票机制确定验证阈值
        1. 每个客户端提出一个阈值候选
        2. 每个客户端对其他人的提案投票（不能投自己）
        3. 得票最多的提案获胜，平票时按激励权重加权平均

        Args:
            client_features: 客户端特征列表

        Returns:
            投票确定的验证阈值
        """
        n_clients = len(client_features)

        # 第一步：每个客户端提出阈值候选
        proposals = []
        for i, features in enumerate(client_features):
            my_avg_change = features['avg_weight_change']
            # 提出比自己稍微宽松的阈值
            proposed_threshold = my_avg_change * 1.3
            proposals.append({
                'client_id': i,
                'threshold': proposed_threshold,
                'proposer_change': my_avg_change
            })

        print(f"    Threshold Proposals:")
        for prop in proposals:
            print(f"      Client {prop['client_id']}: {prop['threshold']:.6f} (based on own change: {prop['proposer_change']:.6f})")

        # 第二步：每个客户端投票（不能投自己）
        votes = {i: [] for i in range(n_clients)}  # proposal_id -> [voter_ids]

        for voter_id in range(n_clients):
            # 选择投票策略：投给最接近自己期望的提案（但不是自己的）
            voter_change = client_features[voter_id]['avg_weight_change']
            voter_preferred_threshold = voter_change * 1.2  # 投票者的期望阈值

            best_proposal_id = None
            best_distance = float('inf')

            for prop_id, proposal in enumerate(proposals):
                if prop_id == voter_id:  # 不能投自己
                    continue

                # 计算与自己期望的距离
                distance = abs(proposal['threshold'] - voter_preferred_threshold)
                if distance < best_distance:
                    best_distance = distance
                    best_proposal_id = prop_id

            if best_proposal_id is not None:
                votes[best_proposal_id].append(voter_id)

        print(f"    Voting Results:")
        for prop_id, voters in votes.items():
            print(f"      Proposal {prop_id} ({proposals[prop_id]['threshold']:.6f}): {len(voters)} votes from {voters}")

        # 第三步：确定获胜提案
        max_votes = max(len(voters) for voters in votes.values())
        winning_proposals = [prop_id for prop_id, voters in votes.items() if len(voters) == max_votes]

        if len(winning_proposals) == 1:
            # 单一获胜者
            winner_id = winning_proposals[0]
            final_threshold = proposals[winner_id]['threshold']
            print(f"    Winner: Proposal {winner_id} with {max_votes} votes, threshold = {final_threshold:.6f}")
        else:
            # 平票情况：按激励权重加权平均
            print(f"    Tie between proposals: {winning_proposals} (each with {max_votes} votes)")
            final_threshold = self._resolve_tie_with_incentives(proposals, winning_proposals, client_features)
            print(f"    Tie resolved with incentive weighting: threshold = {final_threshold:.6f}")

        return final_threshold

    def _two_round_voting_threshold(self, client_features: List[Dict[str, float]]) -> float:
        """
        二轮投票机制确定验证阈值
        如果第一轮投票结果导致大部分客户端无法通过，则进行第二轮投票

        Args:
            client_features: 客户端特征列表

        Returns:
            最终的验证阈值
        """
        print(f"    Starting two-round voting mechanism...")

        # 第一轮投票
        first_threshold = self._real_voting_threshold(client_features)

        # 计算第一轮阈值的通过率
        pass_count = sum(1 for f in client_features if f['avg_weight_change'] <= first_threshold)
        pass_rate = pass_count / len(client_features)

        print(f"    First round: threshold={first_threshold:.6f}, pass_rate={pass_rate:.1%} ({pass_count}/{len(client_features)})")

        # 如果通过率太低，进行第二轮投票
        if pass_rate < 0.8:  # 通过率低于80%
            print(f"    Pass rate too low, starting second round voting...")
            second_threshold = self._second_round_voting(client_features, first_threshold)

            # 验证第二轮结果
            second_pass_count = sum(1 for f in client_features if f['avg_weight_change'] <= second_threshold)
            second_pass_rate = second_pass_count / len(client_features)

            print(f"    Second round: threshold={second_threshold:.6f}, pass_rate={second_pass_rate:.1%} ({second_pass_count}/{len(client_features)})")
            return second_threshold
        else:
            print(f"    First round threshold accepted")
            return first_threshold

    def _second_round_voting(self, client_features: List[Dict[str, float]], first_threshold: float) -> float:
        """
        第二轮投票：采用更宽松的策略

        Args:
            client_features: 客户端特征列表
            first_threshold: 第一轮的阈值

        Returns:
            第二轮投票确定的阈值
        """
        n_clients = len(client_features)

        # 第二轮策略：提出更宽松的阈值
        proposals = []
        for i, features in enumerate(client_features):
            my_avg_change = features['avg_weight_change']

            # 更宽松的策略：给自己和其他客户端更多余量
            proposed_threshold = my_avg_change * 1.8  # 从1.3增加到1.8
            proposals.append({
                'client_id': i,
                'threshold': proposed_threshold,
                'proposer_change': my_avg_change
            })

        top_5_proposals = [f"{p['threshold']:.6f}" for p in sorted(proposals, key=lambda x: x['threshold'])[:5]]
        print(f"      Second round proposals (top 5): {top_5_proposals}")

        # 第二轮投票：更倾向于选择宽松的阈值
        votes = {i: [] for i in range(n_clients)}

        for voter_id in range(n_clients):
            voter_change = client_features[voter_id]['avg_weight_change']

            # 第二轮策略：选择能让自己和大部分人通过的阈值
            voter_preferred_threshold = voter_change * 1.5  # 更宽松的期望

            # 过滤掉极端提案（超过中位数10倍的）
            median_proposal = np.median([p['threshold'] for p in proposals])
            valid_proposals = [(i, p) for i, p in enumerate(proposals)
                             if p['threshold'] <= median_proposal * 10]

            if not valid_proposals:
                valid_proposals = [(i, p) for i, p in enumerate(proposals)]

            best_proposal_id = None
            best_distance = float('inf')

            for prop_id, proposal in valid_proposals:
                if prop_id == voter_id:  # 不能投自己
                    continue

                # 选择既能让自己通过，又相对合理的阈值
                if proposal['threshold'] >= voter_preferred_threshold:
                    distance = abs(proposal['threshold'] - voter_preferred_threshold)
                    if distance < best_distance:
                        best_distance = distance
                        best_proposal_id = prop_id

            # 如果没找到合适的，选择最宽松的（但排除极端值）
            if best_proposal_id is None:
                valid_thresholds = [(i, p['threshold']) for i, p in valid_proposals if i != voter_id]
                if valid_thresholds:
                    best_proposal_id = max(valid_thresholds, key=lambda x: x[1])[0]

            if best_proposal_id is not None:
                votes[best_proposal_id].append(voter_id)

        print(f"      Second round voting results:")
        for prop_id, voters in votes.items():
            if len(voters) > 0:
                print(f"        Proposal {prop_id} ({proposals[prop_id]['threshold']:.6f}): {len(voters)} votes")

        # 确定获胜提案
        max_votes = max(len(voters) for voters in votes.values())
        winning_proposals = [prop_id for prop_id, voters in votes.items() if len(voters) == max_votes]

        if len(winning_proposals) == 1:
            winner_id = winning_proposals[0]
            final_threshold = proposals[winner_id]['threshold']
            print(f"      Second round winner: Proposal {winner_id} with {max_votes} votes")
        else:
            # 平票时选择更宽松的
            winner_id = max(winning_proposals, key=lambda pid: proposals[pid]['threshold'])
            final_threshold = proposals[winner_id]['threshold']
            print(f"      Second round tie resolved: chose more lenient Proposal {winner_id}")

        return final_threshold

    def _resolve_tie_with_incentives(self, proposals: List[Dict], winning_proposal_ids: List[int],
                                   client_features: List[Dict[str, float]]) -> float:
        """
        使用激励机制解决平票
        根据客户端的历史表现计算权重，进行加权平均

        Args:
            proposals: 所有提案
            winning_proposal_ids: 平票的提案ID列表
            client_features: 客户端特征

        Returns:
            加权平均后的阈值
        """
        print(f"      Calculating incentive weights for tied proposals...")

        weighted_sum = 0.0
        total_weight = 0.0

        for prop_id in winning_proposal_ids:
            # 计算该客户端的激励权重
            incentive_weight = self._calculate_incentive_weight(prop_id, client_features)

            threshold = proposals[prop_id]['threshold']
            weighted_sum += threshold * incentive_weight
            total_weight += incentive_weight

            print(f"        Client {prop_id}: threshold={threshold:.6f}, weight={incentive_weight:.3f}")

        final_threshold = weighted_sum / total_weight if total_weight > 0 else np.mean([proposals[pid]['threshold'] for pid in winning_proposal_ids])

        return final_threshold

    def _calculate_incentive_weight(self, client_id: int, client_features: List[Dict[str, float]]) -> float:
        """
        计算客户端的激励权重
        基于历史POL通过率、权重变化稳定性等因素

        Args:
            client_id: 客户端ID
            client_features: 客户端特征

        Returns:
            激励权重 (0.1 到 2.0之间)
        """
        features = client_features[client_id]

        # 基础权重
        base_weight = 1.0

        # 因子1：权重变化稳定性 (变化越小越稳定，权重越高)
        avg_change = features['avg_weight_change']
        stability_factor = 1.0 / (1.0 + avg_change * 100)  # 变化小的客户端权重高

        # 因子2：历史POL通过率 (这里简化，实际应该从历史记录获取)
        # 暂时基于当前特征估算：权重变化合理的客户端假设历史表现好
        if avg_change < 0.002:  # 变化很小，假设历史表现好
            historical_factor = 1.5
        elif avg_change < 0.01:  # 变化适中
            historical_factor = 1.0
        else:  # 变化很大，假设历史表现差
            historical_factor = 0.5

        # 因子3：数据质量 (基于检查点数量等)
        checkpoints = features.get('weight_checkpoints', 1)
        data_quality_factor = min(1.5, checkpoints / 3.0)  # 检查点多的权重高

        # 综合权重
        final_weight = base_weight * stability_factor * historical_factor * data_quality_factor

        # 限制在合理范围内
        final_weight = max(0.1, min(2.0, final_weight))

        return final_weight

    def _calculate_pol_base_weights(self, client_features: List[Dict[str, float]],
                                  verification_results: List[bool]) -> List[float]:
        """
        计算POL基础权重，用于传递给martFL层
        基于POL验证结果、历史信誉、稳定性等因素

        Args:
            client_features: 客户端特征列表
            verification_results: 验证结果列表

        Returns:
            POL基础权重列表
        """
        pol_weights = []

        for i, (features, verified) in enumerate(zip(client_features, verification_results)):
            if not verified:
                # 未通过验证的客户端权重为0
                pol_weights.append(0.0)
                continue

            # 基础权重：通过验证的客户端从1.0开始
            base_weight = 1.0

            # 因子1：权重变化稳定性 (变化越小越稳定，权重越高)
            avg_change = features['avg_weight_change']
            stability_factor = 1.0 / (1.0 + avg_change * 50)  # 稳定性奖励

            # 因子2：历史信誉 (简化版本，基于当前表现估算)
            if avg_change < 0.002:  # 变化很小，假设历史表现好
                reputation_factor = 1.3
            elif avg_change < 0.01:  # 变化适中
                reputation_factor = 1.0
            else:  # 变化较大，假设历史表现一般
                reputation_factor = 0.8

            # 因子3：数据质量 (基于检查点数量等)
            checkpoints = features.get('weight_checkpoints', 1)
            data_quality_factor = min(1.2, checkpoints / 3.0)

            # 综合权重
            pol_weight = base_weight * stability_factor * reputation_factor * data_quality_factor

            # 限制在合理范围内 [0.3, 1.5]
            pol_weight = max(0.3, min(1.5, pol_weight))
            pol_weights.append(pol_weight)

        print(f"    POL base weights: {[f'{w:.3f}' for w in pol_weights]}")
        return pol_weights


    
    def _simple_threshold_comparison(self, client_features: List[Dict[str, float]],
                                   threshold: float) -> List[bool]:
        """
        简单阈值比较验证
        is_verified = client的权重变化 <= threshold

        Args:
            client_features: 客户端特征列表
            threshold: 验证阈值

        Returns:
            每个客户端的验证结果
        """
        results = []

        print(f"    Simple threshold comparison: threshold = {threshold:.6f}")

        for i, features in enumerate(client_features):
            avg_change = features['avg_weight_change']

            # 简单比较：权重变化是否小于等于阈值
            is_verified = avg_change <= threshold
            results.append(is_verified)

            status = "VERIFIED" if is_verified else "REJECTED"
            print(f"    Client {i}: {status} (avg_change: {avg_change:.6f} {'<=' if is_verified else '>'} {threshold:.6f})")

        return results
    
    def _compute_cosine_distance(self, weights1: Dict, weights2: Dict) -> float:
        """
        计算余弦距离 - 按POL论文定义：cos = 1 - cosine_similarity

        Args:
            weights1: 第一组权重
            weights2: 第二组权重

        Returns:
            余弦距离 (0表示完全相同，2表示完全相反)
        """
        # 将所有权重展平为向量
        vec1 = torch.cat([weights1[key].flatten() for key in weights1])
        vec2 = torch.cat([weights2[key].flatten() for key in weights2])

        # 计算余弦相似度
        cos_sim = torch.nn.functional.cosine_similarity(vec1.unsqueeze(0), vec2.unsqueeze(0))

        # POL论文定义：cos = 1 - cosine_similarity
        cosine_distance = 1 - cos_sim.item()

        return cosine_distance

    def _compute_weight_distance(self, weights1: Dict, weights2: Dict) -> float:
        """
        计算权重距离 - 使用余弦距离 (基于POL论文推荐)

        Args:
            weights1: 第一组权重
            weights2: 第二组权重

        Returns:
            权重距离
        """
        if self.distance_metric == 'cosine':
            return self._compute_cosine_distance(weights1, weights2)
        else:
            # L2距离 (备用)
            total_distance = 0.0
            total_params = 0

            for key in weights1:
                if key in weights2:
                    w1 = weights1[key].flatten()
                    w2 = weights2[key].flatten()

                    distance = torch.norm(w2 - w1, p=2).item()
                    total_distance += distance
                    total_params += w1.numel()

            return total_distance / total_params if total_params > 0 else 0.0
    
    def get_verification_stats(self) -> Dict[str, Any]:
        """获取验证统计信息"""
        stats = self.verification_stats.copy()

        if stats['total_verifications'] > 0:
            stats['success_rate'] = stats['successful_verifications'] / stats['total_verifications']
        else:
            stats['success_rate'] = 0.0

        if stats['verification_times']:
            stats['avg_verification_time'] = np.mean(stats['verification_times'])
        else:
            stats['avg_verification_time'] = 0.0

        # 添加阈值演化信息
        stats['current_threshold'] = self.current_threshold
        stats['threshold_history'] = self.threshold_history.copy()
        stats['threshold_stability'] = self._calculate_threshold_stability()

        return stats

    def _calculate_threshold_stability(self) -> float:
        """计算阈值稳定性（变化幅度的倒数）"""
        if len(self.threshold_history) < 2:
            return 1.0

        changes = []
        for i in range(1, len(self.threshold_history)):
            if self.threshold_history[i-1] > 0:
                relative_change = abs(self.threshold_history[i] - self.threshold_history[i-1]) / self.threshold_history[i-1]
                changes.append(relative_change)

        if not changes:
            return 1.0

        avg_change = np.mean(changes)
        stability = 1.0 / (1.0 + avg_change)  # 变化越小，稳定性越高
        return stability
