# Core dependencies for POL-Enhanced Federated Learning
torch>=1.13.0
torchvision>=0.14.0
numpy>=1.21.0
scipy>=1.7.0

# Data handling and processing
pandas>=1.3.0
scikit_learn>=1.0.0

# Visualization
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.0.0

# Configuration and utilities
PyYAML>=6.0
tqdm>=4.60.0

# Cryptography (for POL hashing)
cryptography>=3.4.0

# Clustering algorithms
gap_stat>=2.0.0
kmeans1d>=0.3.0

# Development and testing
pytest>=7.0.0
rich>=13.0.0

# Documentation
sphinx_rtd_theme>=1.3.0

# Additional utilities
setuptools>=65.0.0
torcheval>=0.0.6
torchtext>=0.6.0
ghapi>=1.0.0
nox>=2023.4.0
