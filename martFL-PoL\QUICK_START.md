# Quick Start Guide

Get POL-martFL running in 5 minutes.

## 🚀 Installation

```bash
# Clone and setup
git clone https://github.com/weixiubo/martFL-PoL.git
cd martFL-PoL

# Install dependencies
pip install -r requirements.txt
```

## ⚡ Quick Tests

### 1. Quick Performance Test (Recommended)
```bash
python scripts/quick_comparison_test.py
```
✅ Expected: Enhanced vs Original system comparison

### 2. Enhanced Verifier Testing
```bash
python scripts/test_enhanced_verifier.py
```
📊 Expected: Multi-strategy verifier performance analysis

### 3. Large-Scale Experiment
```bash
python scripts/test_large_scale_multi_round.py
```
🔍 Expected: 24-client, 12-round federated learning simulation

## 📖 Understanding Output

### Verification Results
```
[Byzantine POL Verifier] Starting verification...
  Voting threshold: 0.000272
  17 verified, 2 rejected
```

### Performance Results
```
Enhanced System:
  Precision: 85.7% (+42.9% vs Original)
  Recall: 42.9% (maintained)
  F1-Score: 57.1% (+14.3% vs Original)
  Accuracy: 55.0% (+37.5% vs Original)
```

## 🔧 Key Parameters

```python
integrator = POLMartFLIntegrator(
    device=torch.device('cpu'),
    pol_enabled=True,
    distance_metric='cosine',
    fault_tolerance_ratio=0.33
)
```

## ⚠️ Common Issues

**Import Errors**:
```bash
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
```

**Memory Issues**: Reduce `n_clients` from 24 to 12

**CUDA Issues**:
```bash
export CUDA_VISIBLE_DEVICES=""
```

## 📈 Performance Expectations

- **Small Scale** (6 clients): ~2-3 minutes, F1: 0.6-0.8
- **Large Scale** (24 clients): ~10-15 minutes, F1: 0.6-0.7

---

**Ready to go!** 🎯
