Stack trace:
Frame         Function      Args
0007FFFF9F40  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF8E40) msys-2.0.dll+0x1FE8E
0007FFFF9F40  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA218) msys-2.0.dll+0x67F9
0007FFFF9F40  000210046832 (000210286019, 0007FFFF9DF8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9F40  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9F40  000210068E24 (0007FFFF9F50, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA220  00021006A225 (0007FFFF9F50, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFC92440000 ntdll.dll
7FFC91200000 KERNEL32.DLL
7FFC8FC60000 KERNELBASE.dll
7FFC91BF0000 USER32.dll
7FFC90060000 win32u.dll
7FFC91710000 GDI32.dll
7FFC90090000 gdi32full.dll
7FFC8F630000 msvcp_win.dll
7FFC8F6E0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFC914F0000 advapi32.dll
7FFC90850000 msvcrt.dll
7FFC91EC0000 sechost.dll
7FFC91050000 RPCRT4.dll
7FFC8EBC0000 CRYPTBASE.DLL
7FFC8F8C0000 bcryptPrimitives.dll
7FFC90320000 IMM32.DLL
