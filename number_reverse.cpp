#include<bits/stdc++.h>
using namespace std;

// 反转字符串
string reverseString(string s) {
    reverse(s.begin(), s.end());
    return s;
}

// 去除前导零，但至少保留一个字符
string removeLeadingZeros(string s) {
    while(s.length() > 1 && s[0] == '0') {
        s.erase(0, 1);
    }
    return s;
}

// 去除后导零，但至少保留一个字符
string removeTrailingZeros(string s) {
    while(s.length() > 1 && s.back() == '0') {
        s.pop_back();
    }
    return s;
}

int main(){
    ios::sync_with_stdio(false);
    cin.tie(nullptr);
    
    string input;
    cin >> input;
    
    if(input.find('%') != string::npos) {
        // 百分数处理：反转数字部分，去除前导零
        string num = input.substr(0, input.length() - 1);
        num = reverseString(num);
        num = removeLeadingZeros(num);
        cout << num << "%";
    }
    else if(input.find('/') != string::npos) {
        // 分数处理：分别反转分子和分母，去除前导零
        size_t pos = input.find('/');
        string numerator = input.substr(0, pos);
        string denominator = input.substr(pos + 1);
        
        numerator = reverseString(numerator);
        denominator = reverseString(denominator);
        numerator = removeLeadingZeros(numerator);
        denominator = removeLeadingZeros(denominator);
        
        cout << numerator << "/" << denominator;
    }
    else if(input.find('.') != string::npos) {
        // 小数处理：分别反转整数部分和小数部分
        size_t pos = input.find('.');
        string intPart = input.substr(0, pos);
        string decPart = input.substr(pos + 1);
        
        intPart = reverseString(intPart);
        decPart = reverseString(decPart);
        intPart = removeLeadingZeros(intPart);
        decPart = removeTrailingZeros(decPart);
        
        cout << intPart << "." << decPart;
    }
    else {
        // 整数处理：直接反转，去除前导零
        string result = reverseString(input);
        result = removeLeadingZeros(result);
        cout << result;
    }
    
    return 0;
}
